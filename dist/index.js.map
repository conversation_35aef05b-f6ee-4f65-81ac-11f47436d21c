{"version": 3, "file": "index.js", "sources": ["../src/types/index.ts", "../src/websocket/WebSocketManager.ts", "../src/audio/AudioManager.ts", "../src/utils/MessageParser.ts", "../src/TTSClient.ts", "../src/index.ts"], "sourcesContent": ["// Message types for WebSocket communication\nexport interface TTSMessage {\n  type: 'tts';\n  content: string;\n  role?: string;\n}\n\n// Response message types\nexport type ResponseMessageType = '1000' | '1001';\n\nexport interface TextResponse {\n  type: '1000';\n  content: string;\n}\n\nexport interface BSResponse {\n  type: '1001';\n  content: string;\n}\n\n// Callback function types\nexport type TextCallback = (content: string) => void;\nexport type BSCallback = (content: string) => void;\n\n// WebSocket connection configuration\nexport interface WebSocketConfig {\n  url: string;\n  jwtToken: string;\n  reconnectAttempts?: number;\n  reconnectDelay?: number;\n}\n\n// Audio configuration\nexport interface AudioConfig {\n  sampleRate?: number;\n  channels?: number;\n  bufferSize?: number;\n}\n\n// TTS Client configuration\nexport interface TTSClientConfig {\n  websocket: WebSocketConfig;\n  audio?: AudioConfig;\n  onTextMessage?: TextCallback;\n  onBSMessage?: BSCallback;\n  onError?: (error: Error) => void;\n  onConnect?: () => void;\n  onDisconnect?: () => void;\n}\n\n// WebSocket connection states\nexport enum ConnectionState {\n  DISCONNECTED = 'disconnected',\n  CONNECTING = 'connecting',\n  CONNECTED = 'connected',\n  RECONNECTING = 'reconnecting',\n  ERROR = 'error'\n}\n\n// Audio playback states\nexport enum AudioState {\n  IDLE = 'idle',\n  PLAYING = 'playing',\n  PAUSED = 'paused',\n  STOPPED = 'stopped'\n}\n", "import { WebSocketConfig, ConnectionState, TTSMessage } from '../types';\n\nexport class WebSocketManager {\n  private ws: WebSocket | null = null;\n  private config: WebSocketConfig;\n  private state: ConnectionState = ConnectionState.DISCONNECTED;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts: number;\n  private reconnectDelay: number;\n  private reconnectTimer: NodeJS.Timeout | null = null;\n\n  // Event handlers\n  private onMessageHandler?: (data: ArrayBuffer | string) => void;\n  private onConnectHandler?: () => void;\n  private onDisconnectHandler?: () => void;\n  private onErrorHandler?: (error: Error) => void;\n\n  constructor(config: WebSocketConfig) {\n    this.config = config;\n    this.maxReconnectAttempts = config.reconnectAttempts || 5;\n    this.reconnectDelay = config.reconnectDelay || 3000;\n  }\n\n  public connect(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (this.state === ConnectionState.CONNECTED) {\n        resolve();\n        return;\n      }\n\n      this.state = ConnectionState.CONNECTING;\n      \n      try {\n        this.ws = new WebSocket(this.config.url);\n        \n        // Set binary type to handle ArrayBuffer responses\n        this.ws.binaryType = 'arraybuffer';\n\n        this.ws.onopen = () => {\n          this.state = ConnectionState.CONNECTED;\n          this.reconnectAttempts = 0;\n          \n          // Send JWT token for authentication\n          this.sendAuthToken();\n          \n          this.onConnectHandler?.();\n          resolve();\n        };\n\n        this.ws.onmessage = (event) => {\n          this.handleMessage(event.data);\n        };\n\n        this.ws.onclose = (event) => {\n          this.state = ConnectionState.DISCONNECTED;\n          this.onDisconnectHandler?.();\n          \n          if (!event.wasClean && this.shouldReconnect()) {\n            this.scheduleReconnect();\n          }\n        };\n\n        this.ws.onerror = (event) => {\n          this.state = ConnectionState.ERROR;\n          const error = new Error(`WebSocket error: ${event}`);\n          this.onErrorHandler?.(error);\n          reject(error);\n        };\n\n      } catch (error) {\n        this.state = ConnectionState.ERROR;\n        const wsError = error instanceof Error ? error : new Error('Unknown WebSocket error');\n        this.onErrorHandler?.(wsError);\n        reject(wsError);\n      }\n    });\n  }\n\n  public disconnect(): void {\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    if (this.ws) {\n      this.ws.close(1000, 'Client disconnect');\n      this.ws = null;\n    }\n    \n    this.state = ConnectionState.DISCONNECTED;\n  }\n\n  public sendTTS(content: string, role:string): void {\n    if (this.state !== ConnectionState.CONNECTED || !this.ws) {\n      throw new Error('WebSocket is not connected');\n    }\n\n    const message: TTSMessage = {\n      type: 'tts',\n      content,\n      role\n    };\n\n    this.ws.send(JSON.stringify(message));\n  }\n\n  public getState(): ConnectionState {\n    return this.state;\n  }\n\n  public isConnected(): boolean {\n    return this.state === ConnectionState.CONNECTED;\n  }\n\n  // Event handler setters\n  public onMessage(handler: (data: ArrayBuffer | string) => void): void {\n    this.onMessageHandler = handler;\n  }\n\n  public onConnect(handler: () => void): void {\n    this.onConnectHandler = handler;\n  }\n\n  public onDisconnect(handler: () => void): void {\n    this.onDisconnectHandler = handler;\n  }\n\n  public onError(handler: (error: Error) => void): void {\n    this.onErrorHandler = handler;\n  }\n\n  private sendAuthToken(): void {\n    if (this.ws && this.config.jwtToken) {\n      // Send authentication message with JWT token\n      const authMessage = {\n        type: 'auth',\n        token: this.config.jwtToken\n      };\n      this.ws.send(JSON.stringify(authMessage));\n    }\n  }\n\n  private handleMessage(data: ArrayBuffer | string): void {\n    this.onMessageHandler?.(data);\n  }\n\n  private shouldReconnect(): boolean {\n    return this.reconnectAttempts < this.maxReconnectAttempts;\n  }\n\n  private scheduleReconnect(): void {\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n    }\n\n    this.state = ConnectionState.RECONNECTING;\n    this.reconnectAttempts++;\n\n    this.reconnectTimer = setTimeout(() => {\n      this.connect().catch((error) => {\n        console.error('Reconnection failed:', error);\n        if (this.shouldReconnect()) {\n          this.scheduleReconnect();\n        }\n      });\n    }, this.reconnectDelay);\n  }\n}\n", "import { AudioConfig, AudioState } from '../types';\n\nexport class AudioManager {\n  private audioContext: AudioContext | null = null;\n  private audioWorkletNode: AudioWorkletNode | null = null;\n  private state: AudioState = AudioState.IDLE;\n  private config: AudioConfig;\n  private isInitialized = false;\n\n  // Event handlers\n  private onPlaybackStartedHandler?: () => void;\n  private onPlaybackEndedHandler?: () => void;\n  private onPlaybackStoppedHandler?: () => void;\n  private onErrorHandler?: (error: Error) => void;\n\n  constructor(config: AudioConfig = {}) {\n    this.config = {\n      sampleRate: config.sampleRate || 16000,\n      channels: config.channels || 1,\n      bufferSize: config.bufferSize || 4096,\n      ...config\n    };\n  }\n\n  public async initialize(userGesture: boolean = false): Promise<void> {\n    if (this.isInitialized) {\n      return;\n    }\n\n    try {\n      // Create AudioContext\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({\n        sampleRate: this.config.sampleRate\n      });\n\n      // <PERSON>le suspended context - this is critical for user gesture requirement\n      if (this.audioContext.state === 'suspended') {\n        if (userGesture) {\n          // If we have user gesture, try to resume immediately\n          await this.audioContext.resume();\n        } else {\n          // If no user gesture, we'll defer initialization until first play\n          console.warn('AudioContext is suspended. Will resume on first audio play.');\n          // Don't throw error, just mark as partially initialized\n          this.isInitialized = true;\n          return;\n        }\n      }\n\n      await this.completeInitialization();\n    } catch (error) {\n      const audioError = error instanceof Error ? error : new Error('Failed to initialize audio');\n      this.onErrorHandler?.(audioError);\n      throw audioError;\n    }\n  }\n\n  private async completeInitialization(): Promise<void> {\n    if (!this.audioContext) {\n      throw new Error('AudioContext not created');\n    }\n\n    // Load and register the AudioWorklet processor\n    const processorUrl = this.createProcessorBlobUrl();\n    await this.audioContext.audioWorklet.addModule(processorUrl);\n\n    // Create AudioWorkletNode\n    this.audioWorkletNode = new AudioWorkletNode(\n      this.audioContext,\n      'pcm-audio-processor',\n      {\n        numberOfInputs: 0,\n        numberOfOutputs: 1,\n        outputChannelCount: [this.config.channels || 1]\n      }\n    );\n\n    // Set up message handling\n    this.audioWorkletNode.port.onmessage = (event) => {\n      this.handleWorkletMessage(event.data);\n    };\n\n    // Connect to destination\n    this.audioWorkletNode.connect(this.audioContext.destination);\n\n    this.isInitialized = true;\n  }\n\n  private async ensureFullyInitialized(): Promise<void> {\n    if (!this.audioContext) {\n      await this.initialize(true); // Force initialization with user gesture\n      return;\n    }\n\n    // If AudioContext is suspended, try to resume it\n    if (this.audioContext.state === 'suspended') {\n      try {\n        await this.audioContext.resume();\n        console.log('AudioContext resumed successfully');\n      } catch (error) {\n        throw new Error('AudioContext could not be resumed. User interaction may be required.');\n      }\n    }\n\n    // Complete initialization if not done yet\n    if (!this.audioWorkletNode) {\n      await this.completeInitialization();\n    }\n  }\n\n  public async playAudio(pcmData: ArrayBuffer): Promise<void> {\n    // Ensure full initialization with user gesture context\n    await this.ensureFullyInitialized();\n\n    if (!this.audioWorkletNode) {\n      throw new Error('AudioWorklet not initialized');\n    }\n\n    // Send audio data to processor (streaming mode)\n    this.audioWorkletNode.port.postMessage({\n      type: 'audio-data',\n      payload: pcmData\n    });\n\n    // Only start playback if not already playing\n    if (this.state !== AudioState.PLAYING) {\n      this.audioWorkletNode.port.postMessage({\n        type: 'play'\n      });\n      this.state = AudioState.PLAYING;\n    }\n  }\n\n  public async appendAudio(pcmData: ArrayBuffer): Promise<void> {\n    // Ensure full initialization with user gesture context\n    await this.ensureFullyInitialized();\n\n    if (!this.audioWorkletNode) {\n      throw new Error('AudioWorklet not initialized');\n    }\n\n    // Append audio data without interrupting playback\n    this.audioWorkletNode.port.postMessage({\n      type: 'append-audio-data',\n      payload: pcmData\n    });\n\n    // Auto-start playback if not already playing\n    if (this.state !== AudioState.PLAYING) {\n      this.audioWorkletNode.port.postMessage({\n        type: 'play'\n      });\n      this.state = AudioState.PLAYING;\n    }\n  }\n\n  public stopPlayback(): void {\n    if (!this.audioWorkletNode) {\n      return;\n    }\n\n    this.audioWorkletNode.port.postMessage({\n      type: 'stop'\n    });\n\n    this.state = AudioState.STOPPED;\n  }\n\n  public clearBuffer(): void {\n    if (!this.audioWorkletNode) {\n      return;\n    }\n\n    this.audioWorkletNode.port.postMessage({\n      type: 'clear'\n    });\n\n    this.state = AudioState.IDLE;\n  }\n\n  public async getStats(): Promise<any> {\n    if (!this.audioWorkletNode) {\n      return null;\n    }\n\n    return new Promise((resolve) => {\n      const handleStatsMessage = (event: MessageEvent) => {\n        if (event.data.type === 'stats') {\n          this.audioWorkletNode?.port.removeEventListener('message', handleStatsMessage);\n          resolve(event.data.payload);\n        }\n      };\n\n      if (this.audioWorkletNode) {\n        this.audioWorkletNode.port.addEventListener('message', handleStatsMessage);\n        this.audioWorkletNode.port.postMessage({ type: 'get-stats' });\n      }\n    });\n  }\n\n  public getState(): AudioState {\n    return this.state;\n  }\n\n  public isPlaying(): boolean {\n    return this.state === AudioState.PLAYING;\n  }\n\n  public async enableAudio(): Promise<void> {\n    // This method should be called from a user gesture (click, touch, etc.)\n    if (!this.audioContext) {\n      await this.initialize(true);\n    } else if (this.audioContext.state === 'suspended') {\n      await this.audioContext.resume();\n      console.log('AudioContext enabled by user gesture');\n    }\n  }\n\n  public isAudioEnabled(): boolean {\n    return this.audioContext?.state === 'running';\n  }\n\n  public async dispose(): Promise<void> {\n    this.stopPlayback();\n\n    if (this.audioWorkletNode) {\n      this.audioWorkletNode.disconnect();\n      this.audioWorkletNode = null;\n    }\n\n    if (this.audioContext) {\n      await this.audioContext.close();\n      this.audioContext = null;\n    }\n\n    this.isInitialized = false;\n    this.state = AudioState.IDLE;\n  }\n\n  // Event handler setters\n  public onPlaybackStarted(handler: () => void): void {\n    this.onPlaybackStartedHandler = handler;\n  }\n\n  public onPlaybackEnded(handler: () => void): void {\n    this.onPlaybackEndedHandler = handler;\n  }\n\n  public onPlaybackStopped(handler: () => void): void {\n    this.onPlaybackStoppedHandler = handler;\n  }\n\n  public onError(handler: (error: Error) => void): void {\n    this.onErrorHandler = handler;\n  }\n\n  private handleWorkletMessage(data: any): void {\n    const { type, payload } = data;\n\n    switch (type) {\n      case 'playback-started':\n        this.state = AudioState.PLAYING;\n        this.onPlaybackStartedHandler?.();\n        break;\n\n      case 'playback-ended':\n        this.state = AudioState.IDLE;\n        this.onPlaybackEndedHandler?.();\n        break;\n\n      case 'playback-stopped':\n        this.state = AudioState.STOPPED;\n        this.onPlaybackStoppedHandler?.();\n        break;\n\n      case 'buffer-cleared':\n        this.state = AudioState.IDLE;\n        break;\n\n      case 'error':\n        this.onErrorHandler?.(new Error(payload.message));\n        break;\n    }\n  }\n\n  private createProcessorBlobUrl(): string {\n    // Read the processor code and create a blob URL\n    const processorCode = this.getProcessorCode();\n    const blob = new Blob([processorCode], { type: 'application/javascript' });\n    return URL.createObjectURL(blob);\n  }\n\n  private getProcessorCode(): string {\n    // This would normally be loaded from the separate file\n    // For now, we'll inline it as a string\n    return `\n// AudioWorklet processor for PCM audio playback\nclass PCMAudioProcessor extends AudioWorkletProcessor {\n  constructor() {\n    super();\n    \n    this.audioBuffer = [];\n    this.bufferSize = 0;\n    this.maxBufferSize = 16000 * 5; // Increased to 5 seconds for better streaming\n    this.minBufferSize = 16000 * 0.05; // Reduced to 50ms for faster start\n    \n    this.isPlaying = false;\n    this.shouldStop = false;\n    this.currentPosition = 0;\n    \n    this.targetLatency = 0.1;\n    this.maxLatency = 0.3;\n    this.playbackRate = 1.0;\n    this.smoothingFactor = 0.95;\n    \n    this.underrunCount = 0;\n    this.lastBufferCheck = 0;\n    \n    this.port.onmessage = (event) => {\n      this.handleMessage(event.data);\n    };\n  }\n\n  handleMessage(data) {\n    const { type, payload } = data;\n\n    switch (type) {\n      case 'audio-data':\n        this.addAudioData(payload, true); // Replace existing buffer\n        break;\n      case 'append-audio-data':\n        this.addAudioData(payload, false); // Append to existing buffer\n        break;\n      case 'play':\n        this.startPlayback();\n        break;\n      case 'stop':\n        this.stopPlayback();\n        break;\n      case 'clear':\n        this.clearBuffer();\n        break;\n      case 'get-stats':\n        this.sendStats();\n        break;\n    }\n  }\n\n  addAudioData(pcmData, replaceBuffer = false) {\n    let audioData;\n    if (pcmData instanceof ArrayBuffer) {\n      const int16Array = new Int16Array(pcmData);\n      audioData = new Float32Array(int16Array.length);\n\n      for (let i = 0; i < int16Array.length; i++) {\n        audioData[i] = int16Array[i] / 32768.0;\n      }\n    } else {\n      audioData = new Float32Array(pcmData);\n    }\n\n    if (replaceBuffer) {\n      // Replace existing buffer (for new TTS requests)\n      this.audioBuffer = [...audioData];\n      this.currentPosition = 0;\n    } else {\n      // Append to existing buffer (for streaming audio)\n      this.audioBuffer.push(...audioData);\n    }\n\n    this.bufferSize = this.audioBuffer.length;\n\n    // Trim buffer if it gets too large, but be more conservative\n    if (this.bufferSize > this.maxBufferSize) {\n      const excess = this.bufferSize - this.maxBufferSize;\n      // Only remove from the beginning if we have played past that point\n      const safeRemoveCount = Math.min(excess, Math.floor(this.currentPosition));\n      if (safeRemoveCount > 0) {\n        this.audioBuffer.splice(0, safeRemoveCount);\n        this.bufferSize = this.audioBuffer.length;\n        this.currentPosition -= safeRemoveCount;\n      }\n    }\n\n    // Auto-start playback if we have enough data and not currently playing\n    if (!this.isPlaying && this.bufferSize >= this.minBufferSize) {\n      this.startPlayback();\n    }\n  }\n\n  startPlayback() {\n    this.isPlaying = true;\n    this.shouldStop = false;\n    this.port.postMessage({ type: 'playback-started' });\n  }\n\n  stopPlayback() {\n    this.isPlaying = false;\n    this.shouldStop = true;\n    this.port.postMessage({ type: 'playback-stopped' });\n  }\n\n  clearBuffer() {\n    this.audioBuffer = [];\n    this.bufferSize = 0;\n    this.currentPosition = 0;\n    this.isPlaying = false;\n    this.shouldStop = true;\n    this.port.postMessage({ type: 'buffer-cleared' });\n  }\n\n  sendStats() {\n    const stats = {\n      bufferSize: this.bufferSize,\n      currentPosition: this.currentPosition,\n      isPlaying: this.isPlaying,\n      underrunCount: this.underrunCount,\n      playbackRate: this.playbackRate,\n      bufferedDuration: this.bufferSize / 16000\n    };\n    \n    this.port.postMessage({ type: 'stats', payload: stats });\n  }\n\n  process(inputs, outputs, parameters) {\n    const output = outputs[0];\n    const outputChannel = output[0];\n    const frameCount = outputChannel.length;\n    \n    if (!this.isPlaying || this.shouldStop || this.bufferSize === 0) {\n      outputChannel.fill(0);\n      return true;\n    }\n    \n    this.adjustPlaybackRate();\n    \n    let samplesWritten = 0;\n    for (let i = 0; i < frameCount; i++) {\n      if (this.currentPosition >= this.bufferSize) {\n        if (samplesWritten === 0) {\n          this.underrunCount++;\n        }\n        outputChannel[i] = 0;\n      } else {\n        const sampleIndex = Math.floor(this.currentPosition);\n        const fraction = this.currentPosition - sampleIndex;\n        \n        let sample = this.audioBuffer[sampleIndex];\n        if (sampleIndex + 1 < this.bufferSize && fraction > 0) {\n          const nextSample = this.audioBuffer[sampleIndex + 1];\n          sample = sample * (1 - fraction) + nextSample * fraction;\n        }\n        \n        outputChannel[i] = sample;\n        this.currentPosition += this.playbackRate;\n        samplesWritten++;\n      }\n    }\n    \n    // Clean up consumed samples more frequently for streaming\n    if (this.currentPosition > this.minBufferSize && this.currentPosition > this.bufferSize * 0.3) {\n      const samplesToRemove = Math.floor(this.currentPosition * 0.8); // Keep some overlap\n      if (samplesToRemove > 0) {\n        this.audioBuffer.splice(0, samplesToRemove);\n        this.bufferSize = this.audioBuffer.length;\n        this.currentPosition -= samplesToRemove;\n      }\n    }\n    \n    if (this.currentPosition >= this.bufferSize && this.bufferSize > 0) {\n      this.isPlaying = false;\n      this.port.postMessage({ type: 'playback-ended' });\n    }\n    \n    return true;\n  }\n\n  adjustPlaybackRate() {\n    const currentTime = currentFrame / sampleRate;\n    \n    if (currentTime - this.lastBufferCheck < 0.1) {\n      return;\n    }\n    \n    this.lastBufferCheck = currentTime;\n    \n    const currentLatency = (this.bufferSize - this.currentPosition) / 16000;\n    \n    let targetRate = 1.0;\n    \n    if (currentLatency > this.maxLatency) {\n      targetRate = 1.05;\n    } else if (currentLatency < this.targetLatency * 0.5) {\n      targetRate = 0.98;\n    }\n    \n    this.playbackRate = this.playbackRate * this.smoothingFactor + \n                       targetRate * (1 - this.smoothingFactor);\n    \n    this.playbackRate = Math.max(0.9, Math.min(1.1, this.playbackRate));\n  }\n}\n\nregisterProcessor('pcm-audio-processor', PCMAudioProcessor);\n    `;\n  }\n}\n", "import { Text<PERSON>allback, BS<PERSON>allback, ResponseMessageType } from '../types';\n\nexport interface ParsedMessage {\n  type: 'audio' | 'text' | 'bs' | 'unknown';\n  data: ArrayBuffer | string;\n}\n\nexport class MessageParser {\n  private textCallback?: TextCallback;\n  private bsCallback?: BSCallback;\n\n  constructor(textCallback?: TextCallback, bsCallback?: BSCallback) {\n    this.textCallback = textCallback;\n    this.bsCallback = bsCallback;\n  }\n\n  public setTextCallback(callback: TextCallback): void {\n    this.textCallback = callback;\n  }\n\n  public setBSCallback(callback: BSCallback): void {\n    this.bsCallback = callback;\n  }\n\n  public parseMessage(data: ArrayBuffer | string): ParsedMessage {\n    // Handle ArrayBuffer (audio data)\n    if (data instanceof ArrayBuffer) {\n      return {\n        type: 'audio',\n        data: data\n      };\n    }\n\n    // Handle string messages\n    if (typeof data === 'string') {\n      return this.parseStringMessage(data);\n    }\n\n    // Unknown data type\n    return {\n      type: 'unknown',\n      data: data\n    };\n  }\n\n  public handleMessage(data: Array<PERSON>uffer | string): void {\n    const parsed = this.parseMessage(data);\n\n    switch (parsed.type) {\n      case 'audio':\n        // Audio data is handled separately by the audio manager\n        // This method is primarily for callback handling\n        break;\n\n      case 'text':\n        if (this.textCallback && typeof parsed.data === 'string') {\n          this.textCallback(parsed.data);\n        }\n        break;\n\n      case 'bs':\n        if (this.bsCallback && typeof parsed.data === 'string') {\n          this.bsCallback(parsed.data);\n        }\n        break;\n\n      case 'unknown':\n        console.warn('Received unknown message type:', parsed.data);\n        break;\n    }\n  }\n\n  private parseStringMessage(message: string): ParsedMessage {\n    // Check if message is long enough to have a type prefix\n    if (message.length < 4) {\n      return {\n        type: 'text',\n        data: message\n      };\n    }\n\n    // Extract the first 4 characters to determine message type\n    const typePrefix = message.substring(0, 4);\n    const content = message.substring(4);\n\n    switch (typePrefix) {\n      case '1000':\n        // Regular text message\n        return {\n          type: 'text',\n          data: content\n        };\n\n      case '1001':\n        // BS (Business Service) data message\n        return {\n          type: 'bs',\n          data: content\n        };\n\n      default:\n        // No recognized prefix, treat as regular text\n        return {\n          type: 'text',\n          data: message\n        };\n    }\n  }\n\n  public static isAudioData(data: any): data is ArrayBuffer {\n    return data instanceof ArrayBuffer;\n  }\n\n  public static isTextMessage(message: string): boolean {\n    return message.length >= 4 && message.substring(0, 4) === '1000';\n  }\n\n  public static isBSMessage(message: string): boolean {\n    return message.length >= 4 && message.substring(0, 4) === '1001';\n  }\n\n  public static extractMessageType(message: string): ResponseMessageType | null {\n    if (message.length < 4) {\n      return null;\n    }\n\n    const typePrefix = message.substring(0, 4);\n    \n    if (typePrefix === '1000' || typePrefix === '1001') {\n      return typePrefix as ResponseMessageType;\n    }\n\n    return null;\n  }\n\n  public static extractMessageContent(message: string): string {\n    if (message.length < 4) {\n      return message;\n    }\n\n    const typePrefix = message.substring(0, 4);\n    \n    if (typePrefix === '1000' || typePrefix === '1001') {\n      return message.substring(4);\n    }\n\n    return message;\n  }\n}\n", "import { WebSocketManager } from './websocket/WebSocketManager';\nimport { AudioManager } from './audio/AudioManager';\nimport { MessageParser } from './utils/MessageParser';\nimport { \n  TTSClientConfig, \n  ConnectionState, \n  AudioState,\n  TextCallback,\n  BSCallback \n} from './types';\n\nexport class TTSClient {\n  private wsManager: WebSocketManager;\n  private audioManager: AudioManager;\n  private messageParser: MessageParser;\n  private config: TTSClientConfig;\n  private isInitialized = false;\n  private currentTTSId: string | null = null;\n  private isReceivingAudioStream = false;\n\n  constructor(config: TTSClientConfig) {\n    this.config = config;\n    \n    // Initialize WebSocket manager\n    this.wsManager = new WebSocketManager(config.websocket);\n    \n    // Initialize Audio manager\n    this.audioManager = new AudioManager(config.audio);\n    \n    // Initialize Message parser with callbacks\n    this.messageParser = new MessageParser(\n      config.onTextMessage,\n      config.onBSMessage\n    );\n\n    this.setupEventHandlers();\n  }\n\n  public async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      return;\n    }\n\n    try {\n      // Initialize audio system first\n      await this.audioManager.initialize();\n      \n      // Connect WebSocket\n      await this.wsManager.connect();\n      \n      this.isInitialized = true;\n    } catch (error) {\n      const initError = error instanceof Error ? error : new Error('Initialization failed');\n      this.config.onError?.(initError);\n      throw initError;\n    }\n  }\n\n  public async tts(content: string, role:string): Promise<void> {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n\n    if (!this.wsManager.isConnected()) {\n      throw new Error('WebSocket is not connected');\n    }\n\n    try {\n      // Generate new TTS ID for this request\n      this.currentTTSId = Date.now().toString() + Math.random().toString(36).substring(2, 11);\n\n      // Stop any current audio playback and clear buffer for new TTS\n      this.audioManager.stopPlayback();\n      this.audioManager.clearBuffer();\n\n      // Reset streaming state\n      this.isReceivingAudioStream = false;\n\n      // Send TTS request\n      this.wsManager.sendTTS(content, role);\n    } catch (error) {\n      const ttsError = error instanceof Error ? error : new Error('TTS request failed');\n      this.config.onError?.(ttsError);\n      throw ttsError;\n    }\n  }\n\n  public disconnect(): void {\n    this.wsManager.disconnect();\n  }\n\n  public async dispose(): Promise<void> {\n    this.disconnect();\n    await this.audioManager.dispose();\n    this.isInitialized = false;\n  }\n\n  // Callback setters\n  public setTextCallback(callback: TextCallback): void {\n    this.messageParser.setTextCallback(callback);\n  }\n\n  public setBSCallback(callback: BSCallback): void {\n    this.messageParser.setBSCallback(callback);\n  }\n\n  // Status getters\n  public getConnectionState(): ConnectionState {\n    return this.wsManager.getState();\n  }\n\n  public getAudioState(): AudioState {\n    return this.audioManager.getState();\n  }\n\n  public isConnected(): boolean {\n    return this.wsManager.isConnected();\n  }\n\n  public isPlaying(): boolean {\n    return this.audioManager.isPlaying();\n  }\n\n  public async getAudioStats(): Promise<any> {\n    return await this.audioManager.getStats();\n  }\n\n  public async enableAudio(): Promise<void> {\n    // This method should be called from a user gesture (click, touch, etc.)\n    await this.audioManager.enableAudio();\n  }\n\n  public isAudioEnabled(): boolean {\n    return this.audioManager.isAudioEnabled();\n  }\n\n  private setupEventHandlers(): void {\n    // WebSocket message handling\n    this.wsManager.onMessage((data) => {\n      this.handleWebSocketMessage(data);\n    });\n\n    // WebSocket connection events\n    this.wsManager.onConnect(() => {\n      this.config.onConnect?.();\n    });\n\n    this.wsManager.onDisconnect(() => {\n      this.config.onDisconnect?.();\n    });\n\n    this.wsManager.onError((error) => {\n      this.config.onError?.(error);\n    });\n\n    // Audio playback events\n    this.audioManager.onPlaybackStarted(() => {\n      // Audio playback started\n    });\n\n    this.audioManager.onPlaybackEnded(() => {\n      // Audio playback ended\n    });\n\n    this.audioManager.onPlaybackStopped(() => {\n      // Audio playback stopped\n    });\n\n    this.audioManager.onError((error) => {\n      this.config.onError?.(error);\n    });\n  }\n\n  private async handleWebSocketMessage(data: ArrayBuffer | string): Promise<void> {\n    try {\n      const parsed = this.messageParser.parseMessage(data);\n\n      switch (parsed.type) {\n        case 'audio':\n          // Handle audio data with streaming support\n          if (parsed.data instanceof ArrayBuffer) {\n            if (!this.isReceivingAudioStream) {\n              // First audio chunk - start new playback\n              this.isReceivingAudioStream = true;\n              await this.audioManager.playAudio(parsed.data);\n            } else {\n              // Subsequent audio chunks - append to stream\n              await this.audioManager.appendAudio(parsed.data);\n            }\n          }\n          break;\n\n        case 'text':\n        case 'bs':\n          // Handle text/BS messages through the message parser\n          this.messageParser.handleMessage(data);\n          break;\n\n        case 'unknown':\n          console.warn('Received unknown message type');\n          break;\n      }\n    } catch (error) {\n      const messageError = error instanceof Error ? error : new Error('Message handling failed');\n      this.config.onError?.(messageError);\n    }\n  }\n}\n", "// Main exports\nexport { TTSClient } from './TTSClient';\n\n// Type exports\nexport type {\n  TTSClientConfig,\n  WebSocketConfig,\n  AudioConfig,\n  TextCallback,\n  BSCallback,\n  TTSMessage,\n  TextResponse,\n  BSResponse,\n  ResponseMessageType\n} from './types';\n\n// Enum exports\nexport { ConnectionState, AudioState } from './types';\n\n// Utility exports\nexport { MessageParser } from './utils/MessageParser';\n\n// Component exports (for advanced usage)\nexport { WebSocketManager } from './websocket/WebSocketManager';\nexport { AudioManager } from './audio/AudioManager';\n\n// Default export\nimport { TTSClient } from './TTSClient';\nexport default TTSClient;\n"], "names": ["ConnectionState", "AudioState"], "mappings": ";;;;AAkDA;AACYA;AAAZ,CAAA,UAAY,eAAe,EAAA;AACzB,IAAA,eAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,eAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AACzB,IAAA,eAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,eAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACjB,CAAC,EANWA,uBAAe,KAAfA,uBAAe,GAAA,EAAA,CAAA,CAAA;AAQ3B;AACYC;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACrB,CAAC,EALWA,kBAAU,KAAVA,kBAAU,GAAA,EAAA,CAAA,CAAA;;MC1DT,gBAAgB,CAAA;AAe3B,IAAA,WAAA,CAAY,MAAuB,EAAA;QAd3B,IAAA,CAAA,EAAE,GAAqB,IAAI;AAE3B,QAAA,IAAA,CAAA,KAAK,GAAoBD,uBAAe,CAAC,YAAY;QACrD,IAAA,CAAA,iBAAiB,GAAG,CAAC;QAGrB,IAAA,CAAA,cAAc,GAA0B,IAAI;AASlD,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;QACpB,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,iBAAiB,IAAI,CAAC;QACzD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI;;IAG9C,OAAO,GAAA;QACZ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,IAAI,IAAI,CAAC,KAAK,KAAKA,uBAAe,CAAC,SAAS,EAAE;AAC5C,gBAAA,OAAO,EAAE;gBACT;;AAGF,YAAA,IAAI,CAAC,KAAK,GAAGA,uBAAe,CAAC,UAAU;AAEvC,YAAA,IAAI;AACF,gBAAA,IAAI,CAAC,EAAE,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;;AAGxC,gBAAA,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,aAAa;AAElC,gBAAA,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,MAAK;AACpB,oBAAA,IAAI,CAAC,KAAK,GAAGA,uBAAe,CAAC,SAAS;AACtC,oBAAA,IAAI,CAAC,iBAAiB,GAAG,CAAC;;oBAG1B,IAAI,CAAC,aAAa,EAAE;AAEpB,oBAAA,IAAI,CAAC,gBAAgB,IAAI;AACzB,oBAAA,OAAO,EAAE;AACX,iBAAC;gBAED,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,KAAK,KAAI;AAC5B,oBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC;AAChC,iBAAC;gBAED,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;AAC1B,oBAAA,IAAI,CAAC,KAAK,GAAGA,uBAAe,CAAC,YAAY;AACzC,oBAAA,IAAI,CAAC,mBAAmB,IAAI;oBAE5B,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;wBAC7C,IAAI,CAAC,iBAAiB,EAAE;;AAE5B,iBAAC;gBAED,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;AAC1B,oBAAA,IAAI,CAAC,KAAK,GAAGA,uBAAe,CAAC,KAAK;oBAClC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAA,iBAAA,EAAoB,KAAK,CAAA,CAAE,CAAC;AACpD,oBAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC5B,MAAM,CAAC,KAAK,CAAC;AACf,iBAAC;;YAED,OAAO,KAAK,EAAE;AACd,gBAAA,IAAI,CAAC,KAAK,GAAGA,uBAAe,CAAC,KAAK;AAClC,gBAAA,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAC;AACrF,gBAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;gBAC9B,MAAM,CAAC,OAAO,CAAC;;AAEnB,SAAC,CAAC;;IAGG,UAAU,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;AACjC,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;AAG5B,QAAA,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;AACxC,YAAA,IAAI,CAAC,EAAE,GAAG,IAAI;;AAGhB,QAAA,IAAI,CAAC,KAAK,GAAGA,uBAAe,CAAC,YAAY;;IAGpC,OAAO,CAAC,OAAe,EAAE,IAAW,EAAA;AACzC,QAAA,IAAI,IAAI,CAAC,KAAK,KAAKA,uBAAe,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AACxD,YAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC;;AAG/C,QAAA,MAAM,OAAO,GAAe;AAC1B,YAAA,IAAI,EAAE,KAAK;YACX,OAAO;YACP;SACD;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;;IAGhC,QAAQ,GAAA;QACb,OAAO,IAAI,CAAC,KAAK;;IAGZ,WAAW,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,KAAK,KAAKA,uBAAe,CAAC,SAAS;;;AAI1C,IAAA,SAAS,CAAC,OAA6C,EAAA;AAC5D,QAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO;;AAG1B,IAAA,SAAS,CAAC,OAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO;;AAG1B,IAAA,YAAY,CAAC,OAAmB,EAAA;AACrC,QAAA,IAAI,CAAC,mBAAmB,GAAG,OAAO;;AAG7B,IAAA,OAAO,CAAC,OAA+B,EAAA;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,OAAO;;IAGvB,aAAa,GAAA;QACnB,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;;AAEnC,YAAA,MAAM,WAAW,GAAG;AAClB,gBAAA,IAAI,EAAE,MAAM;AACZ,gBAAA,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC;aACpB;AACD,YAAA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;;;AAIrC,IAAA,aAAa,CAAC,IAA0B,EAAA;AAC9C,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;;IAGvB,eAAe,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB;;IAGnD,iBAAiB,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGnC,QAAA,IAAI,CAAC,KAAK,GAAGA,uBAAe,CAAC,YAAY;QACzC,IAAI,CAAC,iBAAiB,EAAE;AAExB,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAK;YACpC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,KAAI;AAC7B,gBAAA,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAC5C,gBAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;oBAC1B,IAAI,CAAC,iBAAiB,EAAE;;AAE5B,aAAC,CAAC;AACJ,SAAC,EAAE,IAAI,CAAC,cAAc,CAAC;;AAE1B;;MCrKY,YAAY,CAAA;AAavB,IAAA,WAAA,CAAY,SAAsB,EAAE,EAAA;QAZ5B,IAAA,CAAA,YAAY,GAAwB,IAAI;QACxC,IAAA,CAAA,gBAAgB,GAA4B,IAAI;AAChD,QAAA,IAAA,CAAA,KAAK,GAAeC,kBAAU,CAAC,IAAI;QAEnC,IAAA,CAAA,aAAa,GAAG,KAAK;QAS3B,IAAI,CAAC,MAAM,GAAG;AACZ,YAAA,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,KAAK;AACtC,YAAA,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC;AAC9B,YAAA,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI;AACrC,YAAA,GAAG;SACJ;;AAGI,IAAA,MAAM,UAAU,CAAC,WAAA,GAAuB,KAAK,EAAA;AAClD,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB;;AAGF,QAAA,IAAI;;AAEF,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,MAAM,CAAC,YAAY,IAAK,MAAc,CAAC,kBAAkB,EAAE;AAClF,gBAAA,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC;AACzB,aAAA,CAAC;;YAGF,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,WAAW,EAAE;gBAC3C,IAAI,WAAW,EAAE;;AAEf,oBAAA,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;;qBAC3B;;AAEL,oBAAA,OAAO,CAAC,IAAI,CAAC,6DAA6D,CAAC;;AAE3E,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI;oBACzB;;;AAIJ,YAAA,MAAM,IAAI,CAAC,sBAAsB,EAAE;;QACnC,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,4BAA4B,CAAC;AAC3F,YAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;AACjC,YAAA,MAAM,UAAU;;;AAIZ,IAAA,MAAM,sBAAsB,GAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;;;AAI7C,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,EAAE;QAClD,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC;;QAG5D,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAC1C,IAAI,CAAC,YAAY,EACjB,qBAAqB,EACrB;AACE,YAAA,cAAc,EAAE,CAAC;AACjB,YAAA,eAAe,EAAE,CAAC;YAClB,kBAAkB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC;AAC/C,SAAA,CACF;;QAGD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,KAAK,KAAI;AAC/C,YAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC;AACvC,SAAC;;QAGD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;AAE5D,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;AAGnB,IAAA,MAAM,sBAAsB,GAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC5B;;;QAIF,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,WAAW,EAAE;AAC3C,YAAA,IAAI;AACF,gBAAA,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AAChC,gBAAA,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;;YAChD,OAAO,KAAK,EAAE;AACd,gBAAA,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC;;;;AAK3F,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,MAAM,IAAI,CAAC,sBAAsB,EAAE;;;IAIhC,MAAM,SAAS,CAAC,OAAoB,EAAA;;AAEzC,QAAA,MAAM,IAAI,CAAC,sBAAsB,EAAE;AAEnC,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;;;AAIjD,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,YAAA,IAAI,EAAE,YAAY;AAClB,YAAA,OAAO,EAAE;AACV,SAAA,CAAC;;QAGF,IAAI,IAAI,CAAC,KAAK,KAAKA,kBAAU,CAAC,OAAO,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,gBAAA,IAAI,EAAE;AACP,aAAA,CAAC;AACF,YAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,OAAO;;;IAI5B,MAAM,WAAW,CAAC,OAAoB,EAAA;;AAE3C,QAAA,MAAM,IAAI,CAAC,sBAAsB,EAAE;AAEnC,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;;;AAIjD,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,YAAA,IAAI,EAAE,mBAAmB;AACzB,YAAA,OAAO,EAAE;AACV,SAAA,CAAC;;QAGF,IAAI,IAAI,CAAC,KAAK,KAAKA,kBAAU,CAAC,OAAO,EAAE;AACrC,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,gBAAA,IAAI,EAAE;AACP,aAAA,CAAC;AACF,YAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,OAAO;;;IAI5B,YAAY,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B;;AAGF,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,YAAA,IAAI,EAAE;AACP,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,OAAO;;IAG1B,WAAW,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B;;AAGF,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,YAAA,IAAI,EAAE;AACP,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,IAAI;;AAGvB,IAAA,MAAM,QAAQ,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,OAAO,IAAI;;AAGb,QAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC7B,YAAA,MAAM,kBAAkB,GAAG,CAAC,KAAmB,KAAI;gBACjD,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;oBAC/B,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,kBAAkB,CAAC;AAC9E,oBAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;;AAE/B,aAAC;AAED,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,kBAAkB,CAAC;AAC1E,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;;AAEjE,SAAC,CAAC;;IAGG,QAAQ,GAAA;QACb,OAAO,IAAI,CAAC,KAAK;;IAGZ,SAAS,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,KAAK,KAAKA,kBAAU,CAAC,OAAO;;AAGnC,IAAA,MAAM,WAAW,GAAA;;AAEtB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;;aACtB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,WAAW,EAAE;AAClD,YAAA,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AAChC,YAAA,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;;;IAIhD,cAAc,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,KAAK,KAAK,SAAS;;AAGxC,IAAA,MAAM,OAAO,GAAA;QAClB,IAAI,CAAC,YAAY,EAAE;AAEnB,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;AAClC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;AAG9B,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC/B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;AAG1B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,QAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,IAAI;;;AAIvB,IAAA,iBAAiB,CAAC,OAAmB,EAAA;AAC1C,QAAA,IAAI,CAAC,wBAAwB,GAAG,OAAO;;AAGlC,IAAA,eAAe,CAAC,OAAmB,EAAA;AACxC,QAAA,IAAI,CAAC,sBAAsB,GAAG,OAAO;;AAGhC,IAAA,iBAAiB,CAAC,OAAmB,EAAA;AAC1C,QAAA,IAAI,CAAC,wBAAwB,GAAG,OAAO;;AAGlC,IAAA,OAAO,CAAC,OAA+B,EAAA;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,OAAO;;AAGvB,IAAA,oBAAoB,CAAC,IAAS,EAAA;AACpC,QAAA,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI;QAE9B,QAAQ,IAAI;AACV,YAAA,KAAK,kBAAkB;AACrB,gBAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,OAAO;AAC/B,gBAAA,IAAI,CAAC,wBAAwB,IAAI;gBACjC;AAEF,YAAA,KAAK,gBAAgB;AACnB,gBAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,IAAI;AAC5B,gBAAA,IAAI,CAAC,sBAAsB,IAAI;gBAC/B;AAEF,YAAA,KAAK,kBAAkB;AACrB,gBAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,OAAO;AAC/B,gBAAA,IAAI,CAAC,wBAAwB,IAAI;gBACjC;AAEF,YAAA,KAAK,gBAAgB;AACnB,gBAAA,IAAI,CAAC,KAAK,GAAGA,kBAAU,CAAC,IAAI;gBAC5B;AAEF,YAAA,KAAK,OAAO;AACV,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACjD;;;IAIE,sBAAsB,GAAA;;AAE5B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE;AAC7C,QAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;AAC1E,QAAA,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC;;IAG1B,gBAAgB,GAAA;;;QAGtB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiNN;;AAEJ;;MCnfY,aAAa,CAAA;IAIxB,WAAA,CAAY,YAA2B,EAAE,UAAuB,EAAA;AAC9D,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;;AAGvB,IAAA,eAAe,CAAC,QAAsB,EAAA;AAC3C,QAAA,IAAI,CAAC,YAAY,GAAG,QAAQ;;AAGvB,IAAA,aAAa,CAAC,QAAoB,EAAA;AACvC,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ;;AAGrB,IAAA,YAAY,CAAC,IAA0B,EAAA;;AAE5C,QAAA,IAAI,IAAI,YAAY,WAAW,EAAE;YAC/B,OAAO;AACL,gBAAA,IAAI,EAAE,OAAO;AACb,gBAAA,IAAI,EAAE;aACP;;;AAIH,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;;;QAItC,OAAO;AACL,YAAA,IAAI,EAAE,SAAS;AACf,YAAA,IAAI,EAAE;SACP;;AAGI,IAAA,aAAa,CAAC,IAA0B,EAAA;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AAEtC,QAAA,QAAQ,MAAM,CAAC,IAAI;AACjB,YAAA,KAAK,OAAO;;;gBAGV;AAEF,YAAA,KAAK,MAAM;gBACT,IAAI,IAAI,CAAC,YAAY,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AACxD,oBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC;;gBAEhC;AAEF,YAAA,KAAK,IAAI;gBACP,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AACtD,oBAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;;gBAE9B;AAEF,YAAA,KAAK,SAAS;gBACZ,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,MAAM,CAAC,IAAI,CAAC;gBAC3D;;;AAIE,IAAA,kBAAkB,CAAC,OAAe,EAAA;;AAExC,QAAA,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,OAAO;AACL,gBAAA,IAAI,EAAE,MAAM;AACZ,gBAAA,IAAI,EAAE;aACP;;;QAIH,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;QAEpC,QAAQ,UAAU;AAChB,YAAA,KAAK,MAAM;;gBAET,OAAO;AACL,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE;iBACP;AAEH,YAAA,KAAK,MAAM;;gBAET,OAAO;AACL,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,IAAI,EAAE;iBACP;AAEH,YAAA;;gBAEE,OAAO;AACL,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE;iBACP;;;IAIA,OAAO,WAAW,CAAC,IAAS,EAAA;QACjC,OAAO,IAAI,YAAY,WAAW;;IAG7B,OAAO,aAAa,CAAC,OAAe,EAAA;AACzC,QAAA,OAAO,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM;;IAG3D,OAAO,WAAW,CAAC,OAAe,EAAA;AACvC,QAAA,OAAO,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM;;IAG3D,OAAO,kBAAkB,CAAC,OAAe,EAAA;AAC9C,QAAA,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACtB,YAAA,OAAO,IAAI;;QAGb,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QAE1C,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,EAAE;AAClD,YAAA,OAAO,UAAiC;;AAG1C,QAAA,OAAO,IAAI;;IAGN,OAAO,qBAAqB,CAAC,OAAe,EAAA;AACjD,QAAA,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACtB,YAAA,OAAO,OAAO;;QAGhB,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QAE1C,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,EAAE;AAClD,YAAA,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;;AAG7B,QAAA,OAAO,OAAO;;AAEjB;;MCzIY,SAAS,CAAA;AASpB,IAAA,WAAA,CAAY,MAAuB,EAAA;QAJ3B,IAAA,CAAA,aAAa,GAAG,KAAK;QACrB,IAAA,CAAA,YAAY,GAAkB,IAAI;QAClC,IAAA,CAAA,sBAAsB,GAAG,KAAK;AAGpC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;;QAGpB,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC;;QAGvD,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGlD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CACpC,MAAM,CAAC,aAAa,EACpB,MAAM,CAAC,WAAW,CACnB;QAED,IAAI,CAAC,kBAAkB,EAAE;;AAGpB,IAAA,MAAM,UAAU,GAAA;AACrB,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB;;AAGF,QAAA,IAAI;;AAEF,YAAA,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;;AAGpC,YAAA,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAE9B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;QACzB,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,SAAS,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,uBAAuB,CAAC;YACrF,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;AAChC,YAAA,MAAM,SAAS;;;AAIZ,IAAA,MAAM,GAAG,CAAC,OAAe,EAAE,IAAW,EAAA;AAC3C,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACvB,YAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;QAGzB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE;AACjC,YAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC;;AAG/C,QAAA,IAAI;;YAEF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;AAGvF,YAAA,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAChC,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;;AAG/B,YAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;;YAGnC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;;QACrC,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,QAAQ,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAC;YACjF,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC;AAC/B,YAAA,MAAM,QAAQ;;;IAIX,UAAU,GAAA;AACf,QAAA,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;;AAGtB,IAAA,MAAM,OAAO,GAAA;QAClB,IAAI,CAAC,UAAU,EAAE;AACjB,QAAA,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AACjC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;;AAIrB,IAAA,eAAe,CAAC,QAAsB,EAAA;AAC3C,QAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC;;AAGvC,IAAA,aAAa,CAAC,QAAoB,EAAA;AACvC,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC;;;IAIrC,kBAAkB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;;IAG3B,aAAa,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;IAG9B,WAAW,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;;IAG9B,SAAS,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE;;AAG/B,IAAA,MAAM,aAAa,GAAA;AACxB,QAAA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;;AAGpC,IAAA,MAAM,WAAW,GAAA;;AAEtB,QAAA,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;;IAGhC,cAAc,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;;IAGnC,kBAAkB,GAAA;;QAExB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,KAAI;AAChC,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;AACnC,SAAC,CAAC;;AAGF,QAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAK;AAC5B,YAAA,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI;AAC3B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAK;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI;AAC9B,SAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;AAC9B,SAAC,CAAC;;AAGF,QAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAK;;AAEzC,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAK;;AAEvC,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAK;;AAEzC,SAAC,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YAClC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;AAC9B,SAAC,CAAC;;IAGI,MAAM,sBAAsB,CAAC,IAA0B,EAAA;AAC7D,QAAA,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC;AAEpD,YAAA,QAAQ,MAAM,CAAC,IAAI;AACjB,gBAAA,KAAK,OAAO;;AAEV,oBAAA,IAAI,MAAM,CAAC,IAAI,YAAY,WAAW,EAAE;AACtC,wBAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;;AAEhC,4BAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;4BAClC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;;6BACzC;;4BAEL,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;;;oBAGpD;AAEF,gBAAA,KAAK,MAAM;AACX,gBAAA,KAAK,IAAI;;AAEP,oBAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC;oBACtC;AAEF,gBAAA,KAAK,SAAS;AACZ,oBAAA,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC;oBAC7C;;;QAEJ,OAAO,KAAK,EAAE;AACd,YAAA,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAC;YAC1F,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC;;;AAGxC;;AC/MD;;;;;;;;"}