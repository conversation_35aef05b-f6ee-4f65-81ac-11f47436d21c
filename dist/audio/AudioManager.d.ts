import { AudioConfig, AudioState } from '../types';
export declare class AudioManager {
    private audioContext;
    private audioWorkletNode;
    private state;
    private config;
    private isInitialized;
    private onPlaybackStartedHandler?;
    private onPlaybackEndedHandler?;
    private onPlaybackStoppedHandler?;
    private onErrorHandler?;
    constructor(config?: AudioConfig);
    initialize(): Promise<void>;
    playAudio(pcmData: ArrayBuffer): Promise<void>;
    appendAudio(pcmData: ArrayBuffer): Promise<void>;
    stopPlayback(): void;
    clearBuffer(): void;
    getStats(): Promise<any>;
    getState(): AudioState;
    isPlaying(): boolean;
    dispose(): Promise<void>;
    onPlaybackStarted(handler: () => void): void;
    onPlaybackEnded(handler: () => void): void;
    onPlaybackStopped(handler: () => void): void;
    onError(handler: (error: Error) => void): void;
    private handleWorkletMessage;
    private createProcessorBlobUrl;
    private getProcessorCode;
}
