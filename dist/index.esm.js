// WebSocket connection states
var ConnectionState;
(function (ConnectionState) {
    ConnectionState["DISCONNECTED"] = "disconnected";
    ConnectionState["CONNECTING"] = "connecting";
    ConnectionState["CONNECTED"] = "connected";
    ConnectionState["RECONNECTING"] = "reconnecting";
    ConnectionState["ERROR"] = "error";
})(ConnectionState || (ConnectionState = {}));
// Audio playback states
var AudioState;
(function (AudioState) {
    AudioState["IDLE"] = "idle";
    AudioState["PLAYING"] = "playing";
    AudioState["PAUSED"] = "paused";
    AudioState["STOPPED"] = "stopped";
})(AudioState || (AudioState = {}));

class WebSocketManager {
    constructor(config) {
        this.ws = null;
        this.state = ConnectionState.DISCONNECTED;
        this.reconnectAttempts = 0;
        this.reconnectTimer = null;
        this.config = config;
        this.maxReconnectAttempts = config.reconnectAttempts || 5;
        this.reconnectDelay = config.reconnectDelay || 3000;
    }
    connect() {
        return new Promise((resolve, reject) => {
            if (this.state === ConnectionState.CONNECTED) {
                resolve();
                return;
            }
            this.state = ConnectionState.CONNECTING;
            try {
                this.ws = new WebSocket(this.config.url);
                // Set binary type to handle ArrayBuffer responses
                this.ws.binaryType = 'arraybuffer';
                this.ws.onopen = () => {
                    this.state = ConnectionState.CONNECTED;
                    this.reconnectAttempts = 0;
                    // Send JWT token for authentication
                    this.sendAuthToken();
                    this.onConnectHandler?.();
                    resolve();
                };
                this.ws.onmessage = (event) => {
                    this.handleMessage(event.data);
                };
                this.ws.onclose = (event) => {
                    this.state = ConnectionState.DISCONNECTED;
                    this.onDisconnectHandler?.();
                    if (!event.wasClean && this.shouldReconnect()) {
                        this.scheduleReconnect();
                    }
                };
                this.ws.onerror = (event) => {
                    this.state = ConnectionState.ERROR;
                    const error = new Error(`WebSocket error: ${event}`);
                    this.onErrorHandler?.(error);
                    reject(error);
                };
            }
            catch (error) {
                this.state = ConnectionState.ERROR;
                const wsError = error instanceof Error ? error : new Error('Unknown WebSocket error');
                this.onErrorHandler?.(wsError);
                reject(wsError);
            }
        });
    }
    disconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
        this.state = ConnectionState.DISCONNECTED;
    }
    sendTTS(content, role) {
        if (this.state !== ConnectionState.CONNECTED || !this.ws) {
            throw new Error('WebSocket is not connected');
        }
        const message = {
            type: 'tts',
            content,
            role
        };
        this.ws.send(JSON.stringify(message));
    }
    getState() {
        return this.state;
    }
    isConnected() {
        return this.state === ConnectionState.CONNECTED;
    }
    // Event handler setters
    onMessage(handler) {
        this.onMessageHandler = handler;
    }
    onConnect(handler) {
        this.onConnectHandler = handler;
    }
    onDisconnect(handler) {
        this.onDisconnectHandler = handler;
    }
    onError(handler) {
        this.onErrorHandler = handler;
    }
    sendAuthToken() {
        if (this.ws && this.config.jwtToken) {
            // Send authentication message with JWT token
            const authMessage = {
                type: 'auth',
                token: this.config.jwtToken
            };
            this.ws.send(JSON.stringify(authMessage));
        }
    }
    handleMessage(data) {
        this.onMessageHandler?.(data);
    }
    shouldReconnect() {
        return this.reconnectAttempts < this.maxReconnectAttempts;
    }
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        this.state = ConnectionState.RECONNECTING;
        this.reconnectAttempts++;
        this.reconnectTimer = setTimeout(() => {
            this.connect().catch((error) => {
                console.error('Reconnection failed:', error);
                if (this.shouldReconnect()) {
                    this.scheduleReconnect();
                }
            });
        }, this.reconnectDelay);
    }
}

class AudioManager {
    constructor(config = {}) {
        this.audioContext = null;
        this.audioWorkletNode = null;
        this.state = AudioState.IDLE;
        this.isInitialized = false;
        this.config = {
            sampleRate: config.sampleRate || 16000,
            channels: config.channels || 1,
            bufferSize: config.bufferSize || 4096,
            ...config
        };
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            // Create AudioContext
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.config.sampleRate
            });
            // Resume context if it's suspended (required by some browsers)
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            // Load and register the AudioWorklet processor
            const processorUrl = this.createProcessorBlobUrl();
            await this.audioContext.audioWorklet.addModule(processorUrl);
            // Create AudioWorkletNode
            this.audioWorkletNode = new AudioWorkletNode(this.audioContext, 'pcm-audio-processor', {
                numberOfInputs: 0,
                numberOfOutputs: 1,
                outputChannelCount: [this.config.channels || 1]
            });
            // Set up message handling
            this.audioWorkletNode.port.onmessage = (event) => {
                this.handleWorkletMessage(event.data);
            };
            // Connect to destination
            this.audioWorkletNode.connect(this.audioContext.destination);
            this.isInitialized = true;
        }
        catch (error) {
            const audioError = error instanceof Error ? error : new Error('Failed to initialize audio');
            this.onErrorHandler?.(audioError);
            throw audioError;
        }
    }
    async playAudio(pcmData) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        if (!this.audioWorkletNode) {
            throw new Error('AudioWorklet not initialized');
        }
        // Send audio data to processor (streaming mode)
        this.audioWorkletNode.port.postMessage({
            type: 'audio-data',
            payload: pcmData
        });
        // Only start playback if not already playing
        if (this.state !== AudioState.PLAYING) {
            this.audioWorkletNode.port.postMessage({
                type: 'play'
            });
            this.state = AudioState.PLAYING;
        }
    }
    async appendAudio(pcmData) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        if (!this.audioWorkletNode) {
            throw new Error('AudioWorklet not initialized');
        }
        // Append audio data without interrupting playback
        this.audioWorkletNode.port.postMessage({
            type: 'append-audio-data',
            payload: pcmData
        });
        // Auto-start playback if not already playing
        if (this.state !== AudioState.PLAYING) {
            this.audioWorkletNode.port.postMessage({
                type: 'play'
            });
            this.state = AudioState.PLAYING;
        }
    }
    stopPlayback() {
        if (!this.audioWorkletNode) {
            return;
        }
        this.audioWorkletNode.port.postMessage({
            type: 'stop'
        });
        this.state = AudioState.STOPPED;
    }
    clearBuffer() {
        if (!this.audioWorkletNode) {
            return;
        }
        this.audioWorkletNode.port.postMessage({
            type: 'clear'
        });
        this.state = AudioState.IDLE;
    }
    async getStats() {
        if (!this.audioWorkletNode) {
            return null;
        }
        return new Promise((resolve) => {
            const handleStatsMessage = (event) => {
                if (event.data.type === 'stats') {
                    this.audioWorkletNode?.port.removeEventListener('message', handleStatsMessage);
                    resolve(event.data.payload);
                }
            };
            if (this.audioWorkletNode) {
                this.audioWorkletNode.port.addEventListener('message', handleStatsMessage);
                this.audioWorkletNode.port.postMessage({ type: 'get-stats' });
            }
        });
    }
    getState() {
        return this.state;
    }
    isPlaying() {
        return this.state === AudioState.PLAYING;
    }
    async dispose() {
        this.stopPlayback();
        if (this.audioWorkletNode) {
            this.audioWorkletNode.disconnect();
            this.audioWorkletNode = null;
        }
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }
        this.isInitialized = false;
        this.state = AudioState.IDLE;
    }
    // Event handler setters
    onPlaybackStarted(handler) {
        this.onPlaybackStartedHandler = handler;
    }
    onPlaybackEnded(handler) {
        this.onPlaybackEndedHandler = handler;
    }
    onPlaybackStopped(handler) {
        this.onPlaybackStoppedHandler = handler;
    }
    onError(handler) {
        this.onErrorHandler = handler;
    }
    handleWorkletMessage(data) {
        const { type, payload } = data;
        switch (type) {
            case 'playback-started':
                this.state = AudioState.PLAYING;
                this.onPlaybackStartedHandler?.();
                break;
            case 'playback-ended':
                this.state = AudioState.IDLE;
                this.onPlaybackEndedHandler?.();
                break;
            case 'playback-stopped':
                this.state = AudioState.STOPPED;
                this.onPlaybackStoppedHandler?.();
                break;
            case 'buffer-cleared':
                this.state = AudioState.IDLE;
                break;
            case 'error':
                this.onErrorHandler?.(new Error(payload.message));
                break;
        }
    }
    createProcessorBlobUrl() {
        // Read the processor code and create a blob URL
        const processorCode = this.getProcessorCode();
        const blob = new Blob([processorCode], { type: 'application/javascript' });
        return URL.createObjectURL(blob);
    }
    getProcessorCode() {
        // This would normally be loaded from the separate file
        // For now, we'll inline it as a string
        return `
// AudioWorklet processor for PCM audio playback
class PCMAudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    
    this.audioBuffer = [];
    this.bufferSize = 0;
    this.maxBufferSize = 16000 * 5; // Increased to 5 seconds for better streaming
    this.minBufferSize = 16000 * 0.05; // Reduced to 50ms for faster start
    
    this.isPlaying = false;
    this.shouldStop = false;
    this.currentPosition = 0;
    
    this.targetLatency = 0.1;
    this.maxLatency = 0.3;
    this.playbackRate = 1.0;
    this.smoothingFactor = 0.95;
    
    this.underrunCount = 0;
    this.lastBufferCheck = 0;
    
    this.port.onmessage = (event) => {
      this.handleMessage(event.data);
    };
  }

  handleMessage(data) {
    const { type, payload } = data;

    switch (type) {
      case 'audio-data':
        this.addAudioData(payload, true); // Replace existing buffer
        break;
      case 'append-audio-data':
        this.addAudioData(payload, false); // Append to existing buffer
        break;
      case 'play':
        this.startPlayback();
        break;
      case 'stop':
        this.stopPlayback();
        break;
      case 'clear':
        this.clearBuffer();
        break;
      case 'get-stats':
        this.sendStats();
        break;
    }
  }

  addAudioData(pcmData, replaceBuffer = false) {
    let audioData;
    if (pcmData instanceof ArrayBuffer) {
      const int16Array = new Int16Array(pcmData);
      audioData = new Float32Array(int16Array.length);

      for (let i = 0; i < int16Array.length; i++) {
        audioData[i] = int16Array[i] / 32768.0;
      }
    } else {
      audioData = new Float32Array(pcmData);
    }

    if (replaceBuffer) {
      // Replace existing buffer (for new TTS requests)
      this.audioBuffer = [...audioData];
      this.currentPosition = 0;
    } else {
      // Append to existing buffer (for streaming audio)
      this.audioBuffer.push(...audioData);
    }

    this.bufferSize = this.audioBuffer.length;

    // Trim buffer if it gets too large, but be more conservative
    if (this.bufferSize > this.maxBufferSize) {
      const excess = this.bufferSize - this.maxBufferSize;
      // Only remove from the beginning if we have played past that point
      const safeRemoveCount = Math.min(excess, Math.floor(this.currentPosition));
      if (safeRemoveCount > 0) {
        this.audioBuffer.splice(0, safeRemoveCount);
        this.bufferSize = this.audioBuffer.length;
        this.currentPosition -= safeRemoveCount;
      }
    }

    // Auto-start playback if we have enough data and not currently playing
    if (!this.isPlaying && this.bufferSize >= this.minBufferSize) {
      this.startPlayback();
    }
  }

  startPlayback() {
    this.isPlaying = true;
    this.shouldStop = false;
    this.port.postMessage({ type: 'playback-started' });
  }

  stopPlayback() {
    this.isPlaying = false;
    this.shouldStop = true;
    this.port.postMessage({ type: 'playback-stopped' });
  }

  clearBuffer() {
    this.audioBuffer = [];
    this.bufferSize = 0;
    this.currentPosition = 0;
    this.isPlaying = false;
    this.shouldStop = true;
    this.port.postMessage({ type: 'buffer-cleared' });
  }

  sendStats() {
    const stats = {
      bufferSize: this.bufferSize,
      currentPosition: this.currentPosition,
      isPlaying: this.isPlaying,
      underrunCount: this.underrunCount,
      playbackRate: this.playbackRate,
      bufferedDuration: this.bufferSize / 16000
    };
    
    this.port.postMessage({ type: 'stats', payload: stats });
  }

  process(inputs, outputs, parameters) {
    const output = outputs[0];
    const outputChannel = output[0];
    const frameCount = outputChannel.length;
    
    if (!this.isPlaying || this.shouldStop || this.bufferSize === 0) {
      outputChannel.fill(0);
      return true;
    }
    
    this.adjustPlaybackRate();
    
    let samplesWritten = 0;
    for (let i = 0; i < frameCount; i++) {
      if (this.currentPosition >= this.bufferSize) {
        if (samplesWritten === 0) {
          this.underrunCount++;
        }
        outputChannel[i] = 0;
      } else {
        const sampleIndex = Math.floor(this.currentPosition);
        const fraction = this.currentPosition - sampleIndex;
        
        let sample = this.audioBuffer[sampleIndex];
        if (sampleIndex + 1 < this.bufferSize && fraction > 0) {
          const nextSample = this.audioBuffer[sampleIndex + 1];
          sample = sample * (1 - fraction) + nextSample * fraction;
        }
        
        outputChannel[i] = sample;
        this.currentPosition += this.playbackRate;
        samplesWritten++;
      }
    }
    
    // Clean up consumed samples more frequently for streaming
    if (this.currentPosition > this.minBufferSize && this.currentPosition > this.bufferSize * 0.3) {
      const samplesToRemove = Math.floor(this.currentPosition * 0.8); // Keep some overlap
      if (samplesToRemove > 0) {
        this.audioBuffer.splice(0, samplesToRemove);
        this.bufferSize = this.audioBuffer.length;
        this.currentPosition -= samplesToRemove;
      }
    }
    
    if (this.currentPosition >= this.bufferSize && this.bufferSize > 0) {
      this.isPlaying = false;
      this.port.postMessage({ type: 'playback-ended' });
    }
    
    return true;
  }

  adjustPlaybackRate() {
    const currentTime = currentFrame / sampleRate;
    
    if (currentTime - this.lastBufferCheck < 0.1) {
      return;
    }
    
    this.lastBufferCheck = currentTime;
    
    const currentLatency = (this.bufferSize - this.currentPosition) / 16000;
    
    let targetRate = 1.0;
    
    if (currentLatency > this.maxLatency) {
      targetRate = 1.05;
    } else if (currentLatency < this.targetLatency * 0.5) {
      targetRate = 0.98;
    }
    
    this.playbackRate = this.playbackRate * this.smoothingFactor + 
                       targetRate * (1 - this.smoothingFactor);
    
    this.playbackRate = Math.max(0.9, Math.min(1.1, this.playbackRate));
  }
}

registerProcessor('pcm-audio-processor', PCMAudioProcessor);
    `;
    }
}

class MessageParser {
    constructor(textCallback, bsCallback) {
        this.textCallback = textCallback;
        this.bsCallback = bsCallback;
    }
    setTextCallback(callback) {
        this.textCallback = callback;
    }
    setBSCallback(callback) {
        this.bsCallback = callback;
    }
    parseMessage(data) {
        // Handle ArrayBuffer (audio data)
        if (data instanceof ArrayBuffer) {
            return {
                type: 'audio',
                data: data
            };
        }
        // Handle string messages
        if (typeof data === 'string') {
            return this.parseStringMessage(data);
        }
        // Unknown data type
        return {
            type: 'unknown',
            data: data
        };
    }
    handleMessage(data) {
        const parsed = this.parseMessage(data);
        switch (parsed.type) {
            case 'audio':
                // Audio data is handled separately by the audio manager
                // This method is primarily for callback handling
                break;
            case 'text':
                if (this.textCallback && typeof parsed.data === 'string') {
                    this.textCallback(parsed.data);
                }
                break;
            case 'bs':
                if (this.bsCallback && typeof parsed.data === 'string') {
                    this.bsCallback(parsed.data);
                }
                break;
            case 'unknown':
                console.warn('Received unknown message type:', parsed.data);
                break;
        }
    }
    parseStringMessage(message) {
        // Check if message is long enough to have a type prefix
        if (message.length < 4) {
            return {
                type: 'text',
                data: message
            };
        }
        // Extract the first 4 characters to determine message type
        const typePrefix = message.substring(0, 4);
        const content = message.substring(4);
        switch (typePrefix) {
            case '1000':
                // Regular text message
                return {
                    type: 'text',
                    data: content
                };
            case '1001':
                // BS (Business Service) data message
                return {
                    type: 'bs',
                    data: content
                };
            default:
                // No recognized prefix, treat as regular text
                return {
                    type: 'text',
                    data: message
                };
        }
    }
    static isAudioData(data) {
        return data instanceof ArrayBuffer;
    }
    static isTextMessage(message) {
        return message.length >= 4 && message.substring(0, 4) === '1000';
    }
    static isBSMessage(message) {
        return message.length >= 4 && message.substring(0, 4) === '1001';
    }
    static extractMessageType(message) {
        if (message.length < 4) {
            return null;
        }
        const typePrefix = message.substring(0, 4);
        if (typePrefix === '1000' || typePrefix === '1001') {
            return typePrefix;
        }
        return null;
    }
    static extractMessageContent(message) {
        if (message.length < 4) {
            return message;
        }
        const typePrefix = message.substring(0, 4);
        if (typePrefix === '1000' || typePrefix === '1001') {
            return message.substring(4);
        }
        return message;
    }
}

class TTSClient {
    constructor(config) {
        this.isInitialized = false;
        this.currentTTSId = null;
        this.isReceivingAudioStream = false;
        this.config = config;
        // Initialize WebSocket manager
        this.wsManager = new WebSocketManager(config.websocket);
        // Initialize Audio manager
        this.audioManager = new AudioManager(config.audio);
        // Initialize Message parser with callbacks
        this.messageParser = new MessageParser(config.onTextMessage, config.onBSMessage);
        this.setupEventHandlers();
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            // Initialize audio system first
            await this.audioManager.initialize();
            // Connect WebSocket
            await this.wsManager.connect();
            this.isInitialized = true;
        }
        catch (error) {
            const initError = error instanceof Error ? error : new Error('Initialization failed');
            this.config.onError?.(initError);
            throw initError;
        }
    }
    async tts(content, role) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        if (!this.wsManager.isConnected()) {
            throw new Error('WebSocket is not connected');
        }
        try {
            // Generate new TTS ID for this request
            this.currentTTSId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
            // Stop any current audio playback and clear buffer for new TTS
            this.audioManager.stopPlayback();
            this.audioManager.clearBuffer();
            // Reset streaming state
            this.isReceivingAudioStream = false;
            // Send TTS request
            this.wsManager.sendTTS(content, role);
        }
        catch (error) {
            const ttsError = error instanceof Error ? error : new Error('TTS request failed');
            this.config.onError?.(ttsError);
            throw ttsError;
        }
    }
    disconnect() {
        this.wsManager.disconnect();
    }
    async dispose() {
        this.disconnect();
        await this.audioManager.dispose();
        this.isInitialized = false;
    }
    // Callback setters
    setTextCallback(callback) {
        this.messageParser.setTextCallback(callback);
    }
    setBSCallback(callback) {
        this.messageParser.setBSCallback(callback);
    }
    // Status getters
    getConnectionState() {
        return this.wsManager.getState();
    }
    getAudioState() {
        return this.audioManager.getState();
    }
    isConnected() {
        return this.wsManager.isConnected();
    }
    isPlaying() {
        return this.audioManager.isPlaying();
    }
    async getAudioStats() {
        return await this.audioManager.getStats();
    }
    setupEventHandlers() {
        // WebSocket message handling
        this.wsManager.onMessage((data) => {
            this.handleWebSocketMessage(data);
        });
        // WebSocket connection events
        this.wsManager.onConnect(() => {
            this.config.onConnect?.();
        });
        this.wsManager.onDisconnect(() => {
            this.config.onDisconnect?.();
        });
        this.wsManager.onError((error) => {
            this.config.onError?.(error);
        });
        // Audio playback events
        this.audioManager.onPlaybackStarted(() => {
            // Audio playback started
        });
        this.audioManager.onPlaybackEnded(() => {
            // Audio playback ended
        });
        this.audioManager.onPlaybackStopped(() => {
            // Audio playback stopped
        });
        this.audioManager.onError((error) => {
            this.config.onError?.(error);
        });
    }
    async handleWebSocketMessage(data) {
        try {
            const parsed = this.messageParser.parseMessage(data);
            switch (parsed.type) {
                case 'audio':
                    // Handle audio data with streaming support
                    if (parsed.data instanceof ArrayBuffer) {
                        if (!this.isReceivingAudioStream) {
                            // First audio chunk - start new playback
                            this.isReceivingAudioStream = true;
                            await this.audioManager.playAudio(parsed.data);
                        }
                        else {
                            // Subsequent audio chunks - append to stream
                            await this.audioManager.appendAudio(parsed.data);
                        }
                    }
                    break;
                case 'text':
                case 'bs':
                    // Handle text/BS messages through the message parser
                    this.messageParser.handleMessage(data);
                    break;
                case 'unknown':
                    console.warn('Received unknown message type');
                    break;
            }
        }
        catch (error) {
            const messageError = error instanceof Error ? error : new Error('Message handling failed');
            this.config.onError?.(messageError);
        }
    }
}

// Main exports

export { AudioManager, AudioState, ConnectionState, MessageParser, TTSClient, WebSocketManager, TTSClient as default };
//# sourceMappingURL=index.esm.js.map
