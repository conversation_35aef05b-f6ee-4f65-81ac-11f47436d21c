import { WebSocketConfig, ConnectionState } from '../types';
export declare class WebSocketManager {
    private ws;
    private config;
    private state;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private reconnectTimer;
    private onMessageHandler?;
    private onConnectHandler?;
    private onDisconnectHandler?;
    private onErrorHandler?;
    constructor(config: WebSocketConfig);
    connect(): Promise<void>;
    disconnect(): void;
    sendTTS(content: string, role: string): void;
    getState(): ConnectionState;
    isConnected(): boolean;
    onMessage(handler: (data: ArrayBuffer | string) => void): void;
    onConnect(handler: () => void): void;
    onDisconnect(handler: () => void): void;
    onError(handler: (error: Error) => void): void;
    private sendAuthToken;
    private handleMessage;
    private shouldReconnect;
    private scheduleReconnect;
}
