export interface TTSMessage {
    type: 'tts';
    content: string;
    role?: string;
}
export type ResponseMessageType = '1000' | '1001';
export interface TextResponse {
    type: '1000';
    content: string;
}
export interface BSResponse {
    type: '1001';
    content: string;
}
export type TextCallback = (content: string) => void;
export type BSCallback = (content: string) => void;
export interface WebSocketConfig {
    url: string;
    jwtToken: string;
    reconnectAttempts?: number;
    reconnectDelay?: number;
}
export interface AudioConfig {
    sampleRate?: number;
    channels?: number;
    bufferSize?: number;
}
export interface TTSClientConfig {
    websocket: WebSocketConfig;
    audio?: AudioConfig;
    onTextMessage?: TextCallback;
    onBSMessage?: BSCallback;
    onError?: (error: Error) => void;
    onConnect?: () => void;
    onDisconnect?: () => void;
}
export declare enum ConnectionState {
    DISCONNECTED = "disconnected",
    CONNECTING = "connecting",
    CONNECTED = "connected",
    RECONNECTING = "reconnecting",
    ERROR = "error"
}
export declare enum AudioState {
    IDLE = "idle",
    PLAYING = "playing",
    PAUSED = "paused",
    STOPPED = "stopped"
}
