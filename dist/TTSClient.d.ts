import { TTSClientConfig, ConnectionState, AudioState, TextCallback, BSCallback } from './types';
export declare class TTSClient {
    private wsManager;
    private audioManager;
    private messageParser;
    private config;
    private isInitialized;
    private currentTTSId;
    private isReceivingAudioStream;
    constructor(config: TTSClientConfig);
    initialize(): Promise<void>;
    tts(content: string, role: string): Promise<void>;
    disconnect(): void;
    dispose(): Promise<void>;
    setTextCallback(callback: TextCallback): void;
    setBSCallback(callback: BSCallback): void;
    getConnectionState(): ConnectionState;
    getAudioState(): AudioState;
    isConnected(): boolean;
    isPlaying(): boolean;
    getAudioStats(): Promise<any>;
    enableAudio(): Promise<void>;
    isAudioEnabled(): boolean;
    private setupEventHandlers;
    private handleWebSocketMessage;
}
