{"name": "qt-ai-gateway-sdk", "version": "1.0.0", "description": "A WebSocket-based TTS client with real-time audio streaming and playback", "type": "module", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "prepublishOnly": "npm run build"}, "keywords": ["tts", "websocket", "audio", "speech", "real-time", "streaming"], "author": "zhou<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@types/jest": "^29.5.8", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.4", "rollup": "^4.6.1", "tslib": "^2.8.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/tts-websocket-client.git"}, "bugs": {"url": "https://github.com/your-username/tts-websocket-client/issues"}, "homepage": "https://github.com/your-username/tts-websocket-client#readme"}