// Example: Handling Fast Streaming Audio from Server
import TTSClient from '../dist/index.esm.js';

class StreamingAudioExample {
  constructor() {
    this.ttsClient = null;
    this.audioStats = {
      chunksReceived: 0,
      totalBytes: 0,
      playbackStartTime: null,
      lastChunkTime: null
    };
  }

  async initialize() {
    this.ttsClient = new TTSClient({
      websocket: {
        url: 'wss://your-tts-server.com/ws',
        jwtToken: 'your-jwt-token',
        reconnectAttempts: 10,
        reconnectDelay: 2000
      },
      audio: {
        sampleRate: 16000,
        channels: 1,
        bufferSize: 4096
      },
      onConnect: () => {
        console.log('✅ Connected to TTS server');
        this.logStatus('Connected and ready for streaming audio');
      },
      onDisconnect: () => {
        console.log('❌ Disconnected from TTS server');
        this.logStatus('Disconnected - will attempt to reconnect');
      },
      onTextMessage: (content) => {
        console.log('📝 Text message:', content);
        this.logStatus(`Text: ${content}`);
      },
      onBSMessage: (content) => {
        console.log('🔧 BS message:', content);
        this.logStatus(`BS: ${content}`);
        
        // BS messages often indicate audio stream completion
        if (content.includes('completed') || content.includes('finished')) {
          this.onAudioStreamComplete();
        }
      },
      onError: (error) => {
        console.error('❌ TTS Error:', error);
        this.logStatus(`Error: ${error.message}`);
      }
    });

    try {
      await this.ttsClient.initialize();
      this.startStatsMonitoring();
      return true;
    } catch (error) {
      console.error('Failed to initialize TTS client:', error);
      return false;
    }
  }

  async sendTTS(text) {
    if (!this.ttsClient || !this.ttsClient.isConnected()) {
      throw new Error('TTS client not connected');
    }

    // Reset stats for new TTS request
    this.resetAudioStats();
    
    console.log(`🎤 Sending TTS request: "${text.substring(0, 50)}..."`);
    this.logStatus(`Sending TTS: ${text.substring(0, 100)}...`);
    
    try {
      await this.ttsClient.tts(text);
    } catch (error) {
      console.error('TTS request failed:', error);
      throw error;
    }
  }

  resetAudioStats() {
    this.audioStats = {
      chunksReceived: 0,
      totalBytes: 0,
      playbackStartTime: null,
      lastChunkTime: null
    };
  }

  onAudioStreamComplete() {
    const duration = this.audioStats.playbackStartTime ? 
      (Date.now() - this.audioStats.playbackStartTime) / 1000 : 0;
    
    console.log('🎵 Audio stream completed');
    console.log(`📊 Stats: ${this.audioStats.chunksReceived} chunks, ${this.audioStats.totalBytes} bytes, ${duration.toFixed(2)}s`);
    
    this.logStatus(`Stream completed: ${this.audioStats.chunksReceived} chunks in ${duration.toFixed(2)}s`);
  }

  async startStatsMonitoring() {
    setInterval(async () => {
      if (!this.ttsClient) return;

      try {
        const stats = await this.ttsClient.getAudioStats();
        if (stats && this.ttsClient.isPlaying()) {
          const bufferDuration = (stats.bufferedDuration || 0).toFixed(3);
          const playbackRate = (stats.playbackRate || 1.0).toFixed(3);
          
          console.log(`🎵 Audio: Buffer=${bufferDuration}s, Rate=${playbackRate}x, Underruns=${stats.underrunCount || 0}`);
        }
      } catch (error) {
        // Ignore stats errors
      }
    }, 2000);
  }

  logStatus(message) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${message}`);
  }

  async disconnect() {
    if (this.ttsClient) {
      await this.ttsClient.dispose();
      this.ttsClient = null;
    }
  }
}

// Usage example
async function runStreamingExample() {
  const example = new StreamingAudioExample();
  
  console.log('🚀 Starting Streaming Audio Example');
  
  const initialized = await example.initialize();
  if (!initialized) {
    console.error('Failed to initialize');
    return;
  }

  // Wait a bit for connection to stabilize
  await new Promise(resolve => setTimeout(resolve, 1000));

  try {
    // Test with a longer text that will generate streaming audio
    const longText = `
      This is a test of the streaming audio capabilities. 
      The server should send audio data in multiple chunks rapidly, 
      and our client should handle them smoothly without interruption. 
      We expect to receive continuous audio stream that plays seamlessly 
      from start to finish without any gaps or dropouts.
    `.trim();

    await example.sendTTS(longText);
    
    // Keep the example running for a while to see the streaming in action
    console.log('⏳ Waiting for audio stream to complete...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('Example failed:', error);
  } finally {
    await example.disconnect();
    console.log('👋 Example completed');
  }
}

// Export for use in other modules
export { StreamingAudioExample };

// Run example if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runStreamingExample().catch(console.error);
}
