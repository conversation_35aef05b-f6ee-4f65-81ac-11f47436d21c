<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS WebSocket Client Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 TTS WebSocket Client Demo</h1>
        
        <div class="config-section">
            <h3>Configuration</h3>
            <div class="form-group">
                <label for="wsUrl">WebSocket URL:</label>
                <input type="text" id="wsUrl" value="wss://your-tts-server.com/ws" placeholder="wss://your-server.com/ws">
            </div>
            <div class="form-group">
                <label for="jwtToken">JWT Token:</label>
                <input type="text" id="jwtToken" value="" placeholder="Your JWT authentication token">
            </div>
            <button id="connectBtn">Connect</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="enableAudioBtn">🔊 Enable Audio</button>
        </div>
        
        <div id="status" class="status disconnected">Status: Disconnected</div>
        
        <div class="config-section">
            <h3>Text-to-Speech</h3>
            <div class="form-group">
                <label for="ttsText">Text to speak:</label>
                <textarea id="ttsText" placeholder="Enter text to convert to speech...">Hello, this is a test of the TTS WebSocket client. The audio should play smoothly with low latency.</textarea>
            </div>
            <button id="speakBtn" disabled>🔊 Speak</button>
            <button id="stopBtn" disabled>⏹️ Stop</button>
        </div>
        
        <div class="config-section">
            <h3>Message Log</h3>
            <div id="messageLog" class="log"></div>
            <button id="clearLogBtn">Clear Log</button>
        </div>
        
        <div class="config-section">
            <h3>Audio Statistics</h3>
            <div class="stats" id="audioStats">
                <div class="stat-item">
                    <div class="stat-label">Connection State</div>
                    <div class="stat-value" id="connectionState">Disconnected</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Audio State</div>
                    <div class="stat-value" id="audioState">Idle</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Buffer Size</div>
                    <div class="stat-value" id="bufferSize">0</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Playback Rate</div>
                    <div class="stat-value" id="playbackRate">1.0</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the TTS client library -->
    <script type="module">
        // Import the TTS client (in a real implementation, this would be from npm)
        // import TTSClient from 'tts-websocket-client';
        
        // For this demo, we'll simulate the client
        class TTSClientDemo {
            constructor(config) {
                this.config = config;
                this.connected = false;
                this.playing = false;
            }
            
            async initialize() {
                // Simulate initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
                this.connected = true;
                this.config.onConnect?.();
            }
            
            async tts(text) {
                if (!this.connected) throw new Error('Not connected');
                
                this.playing = true;
                
                // Simulate TTS processing
                this.config.onTextMessage?.(`1000Processing: ${text}`);
                
                setTimeout(() => {
                    this.config.onBSMessage?.(`1001Audio generation completed for: ${text.substring(0, 50)}...`);
                }, 500);
                
                // Simulate audio playback
                setTimeout(() => {
                    this.playing = false;
                }, 2000);
            }
            
            disconnect() {
                this.connected = false;
                this.playing = false;
                this.config.onDisconnect?.();
            }
            
            isConnected() { return this.connected; }
            isPlaying() { return this.playing; }
            getConnectionState() { return this.connected ? 'connected' : 'disconnected'; }
            getAudioState() { return this.playing ? 'playing' : 'idle'; }
            
            async getAudioStats() {
                return {
                    bufferSize: Math.floor(Math.random() * 1000),
                    playbackRate: 1.0 + (Math.random() - 0.5) * 0.1,
                    isPlaying: this.playing,
                    bufferedDuration: Math.random() * 0.5
                };
            }
        }
        
        // Demo application
        let ttsClient = null;
        
        // DOM elements
        const wsUrlInput = document.getElementById('wsUrl');
        const jwtTokenInput = document.getElementById('jwtToken');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const enableAudioBtn = document.getElementById('enableAudioBtn');
        const statusDiv = document.getElementById('status');
        const ttsTextArea = document.getElementById('ttsText');
        const speakBtn = document.getElementById('speakBtn');
        const stopBtn = document.getElementById('stopBtn');
        const messageLog = document.getElementById('messageLog');
        const clearLogBtn = document.getElementById('clearLogBtn');
        
        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            messageLog.textContent += logEntry;
            messageLog.scrollTop = messageLog.scrollHeight;
        }
        
        // Update status
        function updateStatus(state, message) {
            statusDiv.className = `status ${state}`;
            statusDiv.textContent = `Status: ${message}`;
        }
        
        // Update statistics
        async function updateStats() {
            if (!ttsClient) return;
            
            document.getElementById('connectionState').textContent = ttsClient.getConnectionState();
            document.getElementById('audioState').textContent = ttsClient.getAudioState();
            
            try {
                const stats = await ttsClient.getAudioStats();
                document.getElementById('bufferSize').textContent = stats.bufferSize;
                document.getElementById('playbackRate').textContent = stats.playbackRate.toFixed(2);
            } catch (error) {
                // Ignore stats errors
            }
        }
        
        // Event handlers
        enableAudioBtn.addEventListener('click', async () => {
            try {
                if (ttsClient) {
                    await ttsClient.enableAudio();
                    log('Audio enabled successfully');
                    enableAudioBtn.textContent = '✅ Audio Enabled';
                    enableAudioBtn.disabled = true;
                } else {
                    log('Please connect first', 'warning');
                }
            } catch (error) {
                log(`Failed to enable audio: ${error.message}`, 'error');
            }
        });

        connectBtn.addEventListener('click', async () => {
            const wsUrl = wsUrlInput.value.trim();
            const jwtToken = jwtTokenInput.value.trim();
            
            if (!wsUrl || !jwtToken) {
                alert('Please enter both WebSocket URL and JWT token');
                return;
            }
            
            try {
                updateStatus('connecting', 'Connecting...');
                connectBtn.disabled = true;
                
                ttsClient = new TTSClientDemo({
                    websocket: {
                        url: wsUrl,
                        jwtToken: jwtToken
                    },
                    onConnect: () => {
                        updateStatus('connected', 'Connected');
                        disconnectBtn.disabled = false;
                        speakBtn.disabled = false;
                        log('Connected to TTS server');
                    },
                    onDisconnect: () => {
                        updateStatus('disconnected', 'Disconnected');
                        connectBtn.disabled = false;
                        disconnectBtn.disabled = true;
                        speakBtn.disabled = true;
                        stopBtn.disabled = true;
                        log('Disconnected from TTS server');
                    },
                    onTextMessage: (content) => {
                        log(`Text message: ${content}`, 'text');
                    },
                    onBSMessage: (content) => {
                        log(`BS message: ${content}`, 'bs');
                    },
                    onError: (error) => {
                        log(`Error: ${error.message}`, 'error');
                        updateStatus('disconnected', 'Error');
                    }
                });
                
                await ttsClient.initialize();
                
            } catch (error) {
                log(`Connection failed: ${error.message}`, 'error');
                updateStatus('disconnected', 'Connection failed');
                connectBtn.disabled = false;
            }
        });
        
        disconnectBtn.addEventListener('click', () => {
            if (ttsClient) {
                ttsClient.disconnect();
                ttsClient = null;
            }
        });
        
        speakBtn.addEventListener('click', async () => {
            const text = ttsTextArea.value.trim();
            if (!text) {
                alert('Please enter text to speak');
                return;
            }
            
            try {
                speakBtn.disabled = true;
                stopBtn.disabled = false;
                log(`TTS request: ${text.substring(0, 100)}...`);
                
                await ttsClient.tts(text);
                
            } catch (error) {
                log(`TTS failed: ${error.message}`, 'error');
            } finally {
                speakBtn.disabled = false;
                stopBtn.disabled = true;
            }
        });
        
        stopBtn.addEventListener('click', () => {
            // In real implementation, this would call ttsClient.stopPlayback()
            log('Audio playback stopped');
            speakBtn.disabled = false;
            stopBtn.disabled = true;
        });
        
        clearLogBtn.addEventListener('click', () => {
            messageLog.textContent = '';
        });
        
        // Update stats periodically
        setInterval(updateStats, 1000);
        
        // Initial log
        log('TTS WebSocket Client Demo loaded');
    </script>
</body>
</html>
