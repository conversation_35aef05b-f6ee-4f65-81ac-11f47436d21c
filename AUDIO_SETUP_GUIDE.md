# Audio Setup Guide

## 解决 AudioContext 用户手势要求

现代浏览器要求 AudioContext 必须在用户交互后才能启动。这是为了防止网页自动播放音频。

### 问题症状

如果你看到以下错误：
```
The AudioContext was not allowed to start. It must be resumed (or created) after a user gesture on the page.
```

### 解决方案

#### 1. 基本设置

```javascript
import TTSClient from 'tts-websocket-client';

const ttsClient = new TTSClient({
  websocket: {
    url: 'wss://your-server.com/ws',
    jwtToken: 'your-token'
  }
});

// 初始化连接（不会启动音频）
await ttsClient.initialize();
```

#### 2. 启用音频（必须在用户手势中调用）

```javascript
// ✅ 正确方式 - 在按钮点击事件中
document.getElementById('enableAudio').addEventListener('click', async () => {
  try {
    await ttsClient.enableAudio();
    console.log('音频已启用');
    
    // 现在可以使用 TTS 了
    await ttsClient.tts('你好，音频已经可以正常工作了！', 'assistant');
  } catch (error) {
    console.error('启用音频失败:', error);
  }
});
```

#### 3. 检查音频状态

```javascript
// 检查音频是否已启用
if (ttsClient.isAudioEnabled()) {
  await ttsClient.tts('音频已就绪', 'system');
} else {
  console.log('请先点击启用音频按钮');
}
```

### 完整示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>TTS 音频设置示例</title>
</head>
<body>
    <button id="enableAudio">🔊 启用音频</button>
    <button id="speak" disabled>🎤 开始语音</button>
    <textarea id="text" placeholder="输入要朗读的文本..."></textarea>
    
    <script type="module">
        import TTSClient from './dist/index.esm.js';
        
        const enableBtn = document.getElementById('enableAudio');
        const speakBtn = document.getElementById('speak');
        const textArea = document.getElementById('text');
        
        // 初始化 TTS 客户端
        const ttsClient = new TTSClient({
          websocket: {
            url: 'wss://your-server.com/ws',
            jwtToken: 'your-token'
          },
          onTextMessage: (msg) => console.log('文本消息:', msg),
          onBSMessage: (msg) => console.log('业务消息:', msg),
          onError: (err) => console.error('错误:', err)
        });
        
        // 连接到服务器
        await ttsClient.initialize();
        
        // 启用音频按钮
        enableBtn.addEventListener('click', async () => {
          try {
            await ttsClient.enableAudio();
            enableBtn.textContent = '✅ 音频已启用';
            enableBtn.disabled = true;
            speakBtn.disabled = false;
            console.log('音频启用成功');
          } catch (error) {
            console.error('音频启用失败:', error);
            alert('音频启用失败: ' + error.message);
          }
        });
        
        // 语音按钮
        speakBtn.addEventListener('click', async () => {
          const text = textArea.value.trim();
          if (!text) {
            alert('请输入要朗读的文本');
            return;
          }
          
          try {
            speakBtn.disabled = true;
            speakBtn.textContent = '🔄 正在处理...';
            
            await ttsClient.tts(text, 'user');
            
          } catch (error) {
            console.error('TTS 失败:', error);
            alert('语音合成失败: ' + error.message);
          } finally {
            speakBtn.disabled = false;
            speakBtn.textContent = '🎤 开始语音';
          }
        });
    </script>
</body>
</html>
```

### 最佳实践

1. **总是在用户交互中启用音频**
   - 按钮点击
   - 触摸事件
   - 键盘事件

2. **提供清晰的用户界面**
   - 显示音频状态
   - 提供启用音频的按钮
   - 给出明确的操作指引

3. **错误处理**
   - 捕获音频启用失败的情况
   - 提供友好的错误提示
   - 允许用户重试

4. **状态管理**
   - 检查音频是否已启用
   - 禁用/启用相关按钮
   - 提供视觉反馈

### 常见问题

**Q: 为什么需要用户手势？**
A: 这是浏览器的安全策略，防止网页自动播放音频打扰用户。

**Q: 可以绕过这个限制吗？**
A: 不可以，这是浏览器的强制要求。必须通过用户交互来启用音频。

**Q: 音频启用后会一直有效吗？**
A: 是的，一旦启用，在页面会话期间会保持有效。

**Q: 如何处理流式音频中断？**
A: 我们的实现已经优化了流式音频处理，避免了中断问题。新的音频数据会自动追加到播放缓冲区。
