// Test for streaming audio functionality
import TTSClient from '../dist/index.esm.js';

describe('Streaming Audio', () => {
  let client;
  let mockAudioChunks;

  beforeEach(() => {
    // Create mock audio chunks (simulating PCM data)
    mockAudioChunks = [
      new ArrayBuffer(1600), // 100ms at 16kHz
      new ArrayBuffer(1600),
      new ArrayBuffer(1600),
      new ArrayBuffer(1600),
      new ArrayBuffer(800)   // 50ms final chunk
    ];

    const config = {
      websocket: {
        url: 'wss://test.example.com/ws',
        jwtToken: 'test-token'
      },
      audio: {
        sampleRate: 16000,
        channels: 1
      }
    };

    client = new TTSClient(config);
  });

  afterEach(async () => {
    if (client) {
      await client.dispose();
    }
  });

  test('should handle rapid audio chunks without interruption', async () => {
    const audioChunksReceived = [];
    let playbackStarted = false;
    let playbackEnded = false;

    // Mock the audio manager to track audio data
    const originalHandleMessage = client.audioManager?.handleWorkletMessage;
    if (client.audioManager) {
      client.audioManager.handleWorkletMessage = (data) => {
        if (data.type === 'playback-started') {
          playbackStarted = true;
        }
        if (data.type === 'playback-ended') {
          playbackEnded = true;
        }
        if (originalHandleMessage) {
          originalHandleMessage.call(client.audioManager, data);
        }
      };
    }

    await client.initialize();

    // Simulate rapid audio chunk arrival
    for (let i = 0; i < mockAudioChunks.length; i++) {
      const chunk = mockAudioChunks[i];
      
      // Simulate WebSocket message with audio data
      await client.handleWebSocketMessage(chunk);
      audioChunksReceived.push(chunk);
      
      // Small delay to simulate network timing
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // Verify that all chunks were processed
    expect(audioChunksReceived.length).toBe(mockAudioChunks.length);
    
    // Note: In a real test environment with proper mocking,
    // we would verify that playback started and audio was continuous
  });

  test('should distinguish between new TTS and streaming chunks', async () => {
    await client.initialize();

    // First TTS request
    await client.tts('First message');
    
    // Simulate first audio chunk (should start new playback)
    await client.handleWebSocketMessage(mockAudioChunks[0]);
    
    // Simulate additional streaming chunks (should append)
    await client.handleWebSocketMessage(mockAudioChunks[1]);
    await client.handleWebSocketMessage(mockAudioChunks[2]);
    
    // New TTS request (should interrupt and start fresh)
    await client.tts('Second message');
    
    // Simulate new audio stream
    await client.handleWebSocketMessage(mockAudioChunks[3]);
    
    // Test passes if no errors are thrown
    expect(true).toBe(true);
  });

  test('should handle mixed message types during streaming', async () => {
    const textMessages = [];
    const bsMessages = [];

    client.setTextCallback((content) => {
      textMessages.push(content);
    });

    client.setBSCallback((content) => {
      bsMessages.push(content);
    });

    await client.initialize();
    await client.tts('Test message');

    // Simulate mixed message stream
    await client.handleWebSocketMessage(mockAudioChunks[0]); // Audio
    await client.handleWebSocketMessage('1000Processing your request'); // Text
    await client.handleWebSocketMessage(mockAudioChunks[1]); // Audio
    await client.handleWebSocketMessage('1001Audio generation completed'); // BS
    await client.handleWebSocketMessage(mockAudioChunks[2]); // Audio

    expect(textMessages).toContain('Processing your request');
    expect(bsMessages).toContain('Audio generation completed');
  });

  test('should maintain audio stats during streaming', async () => {
    await client.initialize();
    await client.tts('Test streaming stats');

    // Simulate audio chunks
    for (const chunk of mockAudioChunks) {
      await client.handleWebSocketMessage(chunk);
    }

    // Get audio stats
    const stats = await client.getAudioStats();
    
    // Stats should be available (even if mocked)
    expect(stats).toBeDefined();
  });
});
