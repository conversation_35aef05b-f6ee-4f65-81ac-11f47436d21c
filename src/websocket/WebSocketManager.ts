import { WebSocketConfig, ConnectionState, TTSMessage } from '../types';

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private state: ConnectionState = ConnectionState.DISCONNECTED;
  private reconnectAttempts = 0;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private reconnectTimer: NodeJS.Timeout | null = null;

  // Event handlers
  private onMessageHandler?: (data: ArrayBuffer | string) => void;
  private onConnectHandler?: () => void;
  private onDisconnectHandler?: () => void;
  private onErrorHandler?: (error: Error) => void;

  constructor(config: WebSocketConfig) {
    this.config = config;
    this.maxReconnectAttempts = config.reconnectAttempts || 5;
    this.reconnectDelay = config.reconnectDelay || 3000;
  }

  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.state === ConnectionState.CONNECTED) {
        resolve();
        return;
      }

      this.state = ConnectionState.CONNECTING;
      
      try {
        this.ws = new WebSocket(this.config.url);
        
        // Set binary type to handle ArrayBuffer responses
        this.ws.binaryType = 'arraybuffer';

        this.ws.onopen = () => {
          this.state = ConnectionState.CONNECTED;
          this.reconnectAttempts = 0;
          
          // Send JWT token for authentication
          this.sendAuthToken();
          
          this.onConnectHandler?.();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          this.state = ConnectionState.DISCONNECTED;
          this.onDisconnectHandler?.();
          
          if (!event.wasClean && this.shouldReconnect()) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (event) => {
          this.state = ConnectionState.ERROR;
          const error = new Error(`WebSocket error: ${event}`);
          this.onErrorHandler?.(error);
          reject(error);
        };

      } catch (error) {
        this.state = ConnectionState.ERROR;
        const wsError = error instanceof Error ? error : new Error('Unknown WebSocket error');
        this.onErrorHandler?.(wsError);
        reject(wsError);
      }
    });
  }

  public disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.state = ConnectionState.DISCONNECTED;
  }

  public sendTTS(content: string, role:string): void {
    if (this.state !== ConnectionState.CONNECTED || !this.ws) {
      throw new Error('WebSocket is not connected');
    }

    const message: TTSMessage = {
      type: 'tts',
      content,
      role
    };

    this.ws.send(JSON.stringify(message));
  }

  public getState(): ConnectionState {
    return this.state;
  }

  public isConnected(): boolean {
    return this.state === ConnectionState.CONNECTED;
  }

  // Event handler setters
  public onMessage(handler: (data: ArrayBuffer | string) => void): void {
    this.onMessageHandler = handler;
  }

  public onConnect(handler: () => void): void {
    this.onConnectHandler = handler;
  }

  public onDisconnect(handler: () => void): void {
    this.onDisconnectHandler = handler;
  }

  public onError(handler: (error: Error) => void): void {
    this.onErrorHandler = handler;
  }

  private sendAuthToken(): void {
    if (this.ws && this.config.jwtToken) {
      // Send authentication message with JWT token
      const authMessage = {
        type: 'auth',
        token: this.config.jwtToken
      };
      this.ws.send(JSON.stringify(authMessage));
    }
  }

  private handleMessage(data: ArrayBuffer | string): void {
    this.onMessageHandler?.(data);
  }

  private shouldReconnect(): boolean {
    return this.reconnectAttempts < this.maxReconnectAttempts;
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.state = ConnectionState.RECONNECTING;
    this.reconnectAttempts++;

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch((error) => {
        console.error('Reconnection failed:', error);
        if (this.shouldReconnect()) {
          this.scheduleReconnect();
        }
      });
    }, this.reconnectDelay);
  }
}
