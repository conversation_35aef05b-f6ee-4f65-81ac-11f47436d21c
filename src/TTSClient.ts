import { WebSocketManager } from './websocket/WebSocketManager';
import { AudioManager } from './audio/AudioManager';
import { MessageParser } from './utils/MessageParser';
import { 
  TTSClientConfig, 
  ConnectionState, 
  AudioState,
  TextCallback,
  BSCallback 
} from './types';

export class TTSClient {
  private wsManager: WebSocketManager;
  private audioManager: AudioManager;
  private messageParser: MessageParser;
  private config: TTSClientConfig;
  private isInitialized = false;
  private currentTTSId: string | null = null;
  private isReceivingAudioStream = false;

  constructor(config: TTSClientConfig) {
    this.config = config;
    
    // Initialize WebSocket manager
    this.wsManager = new WebSocketManager(config.websocket);
    
    // Initialize Audio manager
    this.audioManager = new AudioManager(config.audio);
    
    // Initialize Message parser with callbacks
    this.messageParser = new MessageParser(
      config.onTextMessage,
      config.onBSMessage
    );

    this.setupEventHandlers();
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize audio system first
      await this.audioManager.initialize();
      
      // Connect WebSocket
      await this.wsManager.connect();
      
      this.isInitialized = true;
    } catch (error) {
      const initError = error instanceof Error ? error : new Error('Initialization failed');
      this.config.onError?.(initError);
      throw initError;
    }
  }

  public async tts(content: string, role:string): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.wsManager.isConnected()) {
      throw new Error('WebSocket is not connected');
    }

    try {
      // Generate new TTS ID for this request
      this.currentTTSId = Date.now().toString() + Math.random().toString(36).substring(2, 11);

      // Stop any current audio playback and clear buffer for new TTS
      this.audioManager.stopPlayback();
      this.audioManager.clearBuffer();

      // Reset streaming state
      this.isReceivingAudioStream = false;

      // Send TTS request
      this.wsManager.sendTTS(content, role);
    } catch (error) {
      const ttsError = error instanceof Error ? error : new Error('TTS request failed');
      this.config.onError?.(ttsError);
      throw ttsError;
    }
  }

  public disconnect(): void {
    this.wsManager.disconnect();
  }

  public async dispose(): Promise<void> {
    this.disconnect();
    await this.audioManager.dispose();
    this.isInitialized = false;
  }

  // Callback setters
  public setTextCallback(callback: TextCallback): void {
    this.messageParser.setTextCallback(callback);
  }

  public setBSCallback(callback: BSCallback): void {
    this.messageParser.setBSCallback(callback);
  }

  // Status getters
  public getConnectionState(): ConnectionState {
    return this.wsManager.getState();
  }

  public getAudioState(): AudioState {
    return this.audioManager.getState();
  }

  public isConnected(): boolean {
    return this.wsManager.isConnected();
  }

  public isPlaying(): boolean {
    return this.audioManager.isPlaying();
  }

  public async getAudioStats(): Promise<any> {
    return await this.audioManager.getStats();
  }

  public async enableAudio(): Promise<void> {
    // This method should be called from a user gesture (click, touch, etc.)
    await this.audioManager.enableAudio();
  }

  public isAudioEnabled(): boolean {
    return this.audioManager.isAudioEnabled();
  }

  private setupEventHandlers(): void {
    // WebSocket message handling
    this.wsManager.onMessage((data) => {
      this.handleWebSocketMessage(data);
    });

    // WebSocket connection events
    this.wsManager.onConnect(() => {
      this.config.onConnect?.();
    });

    this.wsManager.onDisconnect(() => {
      this.config.onDisconnect?.();
    });

    this.wsManager.onError((error) => {
      this.config.onError?.(error);
    });

    // Audio playback events
    this.audioManager.onPlaybackStarted(() => {
      // Audio playback started
    });

    this.audioManager.onPlaybackEnded(() => {
      // Audio playback ended
    });

    this.audioManager.onPlaybackStopped(() => {
      // Audio playback stopped
    });

    this.audioManager.onError((error) => {
      this.config.onError?.(error);
    });
  }

  private async handleWebSocketMessage(data: ArrayBuffer | string): Promise<void> {
    try {
      const parsed = this.messageParser.parseMessage(data);

      switch (parsed.type) {
        case 'audio':
          // Handle audio data with streaming support
          if (parsed.data instanceof ArrayBuffer) {
            if (!this.isReceivingAudioStream) {
              // First audio chunk - start new playback
              this.isReceivingAudioStream = true;
              await this.audioManager.playAudio(parsed.data);
            } else {
              // Subsequent audio chunks - append to stream
              await this.audioManager.appendAudio(parsed.data);
            }
          }
          break;

        case 'text':
        case 'bs':
          // Handle text/BS messages through the message parser
          this.messageParser.handleMessage(data);
          break;

        case 'unknown':
          console.warn('Received unknown message type');
          break;
      }
    } catch (error) {
      const messageError = error instanceof Error ? error : new Error('Message handling failed');
      this.config.onError?.(messageError);
    }
  }
}
