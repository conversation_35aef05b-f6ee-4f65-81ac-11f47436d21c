// Message types for WebSocket communication
export interface TTSMessage {
  type: 'tts';
  content: string;
  role?: string;
}

// Response message types
export type ResponseMessageType = '1000' | '1001';

export interface TextResponse {
  type: '1000';
  content: string;
}

export interface BSResponse {
  type: '1001';
  content: string;
}

// Callback function types
export type TextCallback = (content: string) => void;
export type BSCallback = (content: string) => void;

// WebSocket connection configuration
export interface WebSocketConfig {
  url: string;
  jwtToken: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

// Audio configuration
export interface AudioConfig {
  sampleRate?: number;
  channels?: number;
  bufferSize?: number;
}

// TTS Client configuration
export interface TTSClientConfig {
  websocket: WebSocketConfig;
  audio?: AudioConfig;
  onTextMessage?: TextCallback;
  onBSMessage?: BSCallback;
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

// WebSocket connection states
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// Audio playback states
export enum AudioState {
  IDLE = 'idle',
  PLAYING = 'playing',
  PAUSED = 'paused',
  STOPPED = 'stopped'
}
